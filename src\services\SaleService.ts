export interface ISale extends IDefaultFields {
   id: number;
   product_list: ISaleProduct[];
   installment_list: ISaleInstallment[];
   total_amount: string;
   debit: string;
   installment: number;
   installment_type: string;
   notes?: string;
   sale_date: string;
   installment_start_date: string;
   pre_payment?: number;
   customer: {
      id: number;
      name: string;
      surname: string;
      email?: string;
      phone?: string;
   };
}

export interface ISaleStore {
   id?: number;
   customer_id: number;
   total_amount: number;
   debit?: number;
   pre_payment?: number;
   sale_product: { id: number; quantity: number; unit_price: number }[];
   installment: number;
   installment_type: string;
   notes?: string;
   sale_date: string;
   installment_start_date: string;
}

export interface ISaleProduct extends IDefaultFields {
   sale_id: number;
   id: number;
   price: string;
   quantity: number;
   code: string;
   title: string;
}

export interface ISaleInstallment extends IDefaultFields {
   sale_id: number;
   id: number;
   amount: string;
   payment: string;
   status: string;
   due_at: string;
   paid_at: string;
}

export const useGetSaleAll = (payload?: TQuery<ISale[]>) => {
   const options = computed(() => ({
      queryKey: ["sale", "saleAll"],
      queryFn: async () => {
         return (await appAxios.get("/sale/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetSaleById = (payload?: { id?: MaybeRef<string | number> } & TQuery<ISale>) => {
   const options = computed(() => ({
      queryKey: ["sale", "saleById", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/sale/${toValue(payload?.id)}`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateSale = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["sale", "updateSale"],
      mutationFn: async (data: ISaleStore): Promise<TResponse<ISale>> => {
         return (await appAxios.put("/sale/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["sale"] });
      }
   });
};

export const useCreateSale = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["sale", "createSale"],
      mutationFn: async (data: ISaleStore): Promise<TResponse<ISale>> => {
         return (await appAxios.post("/sale/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["sale"] });
      }
   });
};

export const useDeleteSale = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["sale", "deleteSale"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/sale/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["sale"] });
      }
   });
};
