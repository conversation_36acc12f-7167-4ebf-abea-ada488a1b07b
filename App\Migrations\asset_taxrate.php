<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_taxrate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_taxrate` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `title` VARCHAR(250) NOT NULL,
         `rate` DECIMAL(5, 2) NOT NULL DEFAULT 0,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('asset_taxrate')->insert([
         'title' => 'KDV %20',
         'rate' => 20.00,
         'is_active' => 1,
         'sort_order' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_taxrate')->insert([
         'title' => 'KDV %10',
         'rate' => 10.00,
         'is_active' => 1,
         'sort_order' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_taxrate`");
   }
}