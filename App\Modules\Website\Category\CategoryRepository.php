<?php

declare(strict_types=1);

namespace App\Modules\Website\Category;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CategoryRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'web_category'
   ) {
   }

   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_category.*,
               asset_category_translate.*,
               web_category_translate.*,
               COALESCE(web_category.is_active, asset_category.is_active) AS is_active,
               COALESCE(web_category.sort_order, asset_category.sort_order) AS sort_order,
               COALESCE(web_category.parent_id, asset_category.parent_id) AS parent_id,
               COALESCE(web_category_translate.title, asset_category_translate.title) AS title,
               asset_category_translate.title AS origin_title
            FROM asset_category

            LEFT JOIN web_category ON web_category.category_id = asset_category.id
               AND web_category.deleted_at IS NULL
            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_category.id
               AND asset_category_translate.language_id = :language_id_inv
            LEFT JOIN web_category_translate ON web_category_translate.category_id = asset_category.id
               AND web_category_translate.language_id = :language_id_web
            WHERE asset_category.deleted_at IS NULL
               AND asset_category.is_active = 1
         ')
         ->execute([
            'language_id_inv' => $lang_id,
            'language_id_web' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_category.*,
               asset_category_translate.*,
               web_category_translate.*,
               COALESCE(web_category.is_active, asset_category.is_active) AS is_active,
               COALESCE(web_category.sort_order, asset_category.sort_order) AS sort_order,
               COALESCE(web_category.parent_id, asset_category.parent_id) AS parent_id,
               COALESCE(web_category_translate.title, asset_category_translate.title) AS title,
               asset_category_translate.title AS origin_title
            FROM asset_category

            LEFT JOIN web_category ON web_category.category_id = asset_category.id
               AND web_category.deleted_at IS NULL
            LEFT JOIN asset_category_translate ON asset_category_translate.category_id = asset_category.id
               AND asset_category_translate.language_id = :language_id_inv
            LEFT JOIN asset_category_translate AS asset_default_translate ON asset_default_translate.category_id = asset_category.id
               AND asset_default_translate.language_id = 1
            LEFT JOIN web_category_translate ON web_category_translate.category_id = asset_category.id
               AND web_category_translate.language_id = :language_id_web
            LEFT JOIN web_category_translate AS web_default_translate ON web_default_translate.category_id = asset_category.id
               AND web_default_translate.language_id = 1
            WHERE asset_category.deleted_at IS NULL
               AND asset_category.is_active = 1
               AND asset_category.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id_inv' => $lang_id,
            'language_id_web' => $lang_id
         ])
         ->fetch();
   }
}
