<?php

declare(strict_types=1);

use System\Migration\Migration;

class web_product_category extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `web_product_category` (
         `category_id` INT NOT NULL DEFAULT 0,
         `product_id` INT NOT NULL DEFAULT 0,
          PRIMARY KEY (`category_id`, `product_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `web_product_category`");
   }
}
