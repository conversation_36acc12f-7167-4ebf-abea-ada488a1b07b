<?php

declare(strict_types=1);

namespace App\Modules\Asset\Unit;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Asset\Unit\UnitRequest;
use App\Modules\Asset\Unit\UnitRepository;

class UnitService extends BaseService {
   /** @var UnitRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      UnitRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function createUnit(UnitRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->validate($request->toArray(), [
            'title' => 'required',
            'type' => 'required|numeric',
            'intl_id' => 'required|numeric'
         ]);

         $id = $this->create([
            'title' => $request->title,
            'type' => $request->type,
            'intl_id' => $request->intl_id,
         ]);

         return $this->getOne($id);
      });
   }

   public function updateUnit(UnitRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'title' => 'required',
            'type' => 'required|numeric',
            'intl_id' => 'required|numeric'
         ]);

         $this->update($request, [
            'title' => $request->title,
            'type' => $request->type,
            'intl_id' => $request->intl_id,
         ], [
            'id' => $request->id
         ]);

         return $this->getOne($request->id);
      });
   }
}
