<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
            <RecordButton
               v-bind:disabled="isLoading"
               color="primary"
               @click="recordHandler">
               {{ t("app.add") }}
            </RecordButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="flex-center text-base">{{ t("app.categoryList") }}</v-card-title>
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:hover="false"
            v-bind:items="data">
            <template v-slot:item.is_active="{ item }">
               <v-chip v-bind:color="item.is_active ? 'success' : undefined">
                  {{ item.is_active ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>

            <template v-slot:item.actions="{ item }: { item: ICategory }">
               <ActionButton
                  icon="$trash"
                  @click="removeHandler(item)" />
               <ActionButton
                  icon="$edit"
                  @click="recordHandler(item)" />
            </template>
         </DataTable>
      </Card>

      <CategoryDialog ref="dialog" />
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import RecordButton from "@/components/Button/RecordButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ICategory, useDeleteCategory, useGetCategoryAll } from "../services/CategoryService";
const CategoryDialog = defineAsyncComponent(() => import("@/components/Dialog/CategoryDialog.vue"));

// hooks
const { t } = useI18n();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const dialog = ref<InstanceType<typeof CategoryDialog>>();
const filter = ref();
const selected = ref([]);
const headers = computed((): THeader<ICategory>[] => [
   { title: t("app.code"), key: "code", width: "150" },
   { title: t("app.title"), key: "title" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("app.createDate"), key: "created_at", width: "200", date: true },
   { title: t("app.updateDate"), key: "updated_at", width: "200", date: true },
   { key: "actions", width: "60" }
]);

// services
const { data, isLoading } = useGetCategoryAll();
const deleteCategory = useDeleteCategory();

// handlers
const recordHandler = (item?: ICategory) => {
   dialog.value?.open(item);
};

const removeHandler = async (item: ICategory) => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteRecord")
      });

      if (confirm) {
         await deleteCategory.mutateAsync(item.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>
