<?php

declare(strict_types=1);

namespace App\Modules\Asset\Attr;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class AttrRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'asset_attr'
   ) {
   }

   // override
   public function findAll(int $lang_id = 1): array {
      return $this->database
         ->prepare('SELECT
               asset_attr.*,
               asset_attr_translate.*
            FROM asset_attr

            LEFT JOIN asset_attr_translate ON asset_attr_translate.attr_id = asset_attr.id
               AND asset_attr_translate.language_id = :language_id
            WHERE asset_attr.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   // override
   public function findOne(int $id, int $lang_id = 1): array|false {
      return $this->database
         ->prepare('SELECT
               asset_attr.*,
               asset_attr_translate.*,
               COALESCE(asset_attr_translate.title, default_translate.title) AS `title`
            FROM asset_attr

            LEFT JOIN asset_attr_translate ON asset_attr_translate.attr_id = asset_attr.id
               AND asset_attr_translate.language_id = :language_id
            LEFT JOIN asset_attr_translate AS default_translate ON default_translate.attr_id = asset_attr.id
               AND default_translate.language_id = 1
            WHERE asset_attr.deleted_at IS NULL
               AND asset_attr.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
