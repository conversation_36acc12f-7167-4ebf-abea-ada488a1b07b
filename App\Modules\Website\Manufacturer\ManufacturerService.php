<?php

declare(strict_types=1);

namespace App\Modules\Website\Manufacturer;

use System\Upload\Upload;
use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Website\Manufacturer\ManufacturerRequest;
use App\Modules\Website\Manufacturer\ManufacturerRepository;

class ManufacturerService extends BaseService {
   /** @var ManufacturerRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Upload $upload,
      protected Validation $validation,
      ManufacturerRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function overrideManufacturer(ManufacturerRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'is_active' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'url' => 'required',
            'meta_title' => 'required',
            'meta_description' => 'required',
            'meta_keywords' => 'required'
         ]);

         $check = $this->repository->findBy(['manufacturer_id' => $request->manufacturer_id]);

         if (empty($check)) {
            $result = $this->repository->create([
               'manufacturer_id' => $request->manufacturer_id,
               'is_active' => $request->is_active,
               'sort_order' => $request->sort_order,
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to create the web manufacturer record', 400);
            }
         } else {
            $result = $this->repository->update([
               'is_active' => $request->is_active,
               'sort_order' => $request->sort_order,
            ], [
               'manufacturer_id' => $request->manufacturer_id
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to update the web manufacturer record', 400);
            }
         }

         $this->translate($request->translate, [
            'manufacturer_id' => $request->manufacturer_id,
         ], 'web_manufacturer_translate');

         return $this->getOne($request->manufacturer_id, $lang_id);
      });
   }

   public function delete(array $where, ?string $table = null): bool {
      return $this->transaction(function () use ($where, $table) {
         $this->repository->hardDelete($where, $table);
         $this->repository->hardDelete($where, 'web_manufacturer_translate');

         return true;
      });
   }
}
