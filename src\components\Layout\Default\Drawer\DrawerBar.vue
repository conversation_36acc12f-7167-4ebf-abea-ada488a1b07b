<template>
   <v-navigation-drawer
      v-bind:class="{ '!w-60': expand }"
      class="bg-dark pt-2 select-none drawer-content:flex drawer-content:flex-col"
      color="background"
      rail
      theme="dark">
      <div class="flex-center w-14">
         <v-btn
            v-bind:active="expand"
            icon
            @click="expand = !expand">
            <ToggleIcon
               v-bind:icon="['$menu', '$close']"
               v-bind:toggle="!expand" />
         </v-btn>
      </div>

      <v-list class="flex flex-1 flex-col">
         <v-list-item
            v-for="item in appMenu"
            v-bind="item.itemProps"
            v-bind:active="appStore.module === item.itemProps?.value"
            @click="moduleHandler(item.itemProps?.value)">
            <template v-slot:prepend>
               <v-icon
                  v-bind:icon="item.itemProps?.prependIcon"
                  size="default" />
            </template>

            <v-list-item-title>{{ t(item.itemTitle as string) }}</v-list-item-title>
         </v-list-item>

         <v-spacer />
         <v-list-item link>
            <template v-slot:prepend>
               <div class="relative me-5 h-6 w-6">
                  <v-avatar
                     class="absolute -top-0.5 -left-0.5"
                     size="28px">
                     <v-img src="@/assets/image/man.svg" />
                  </v-avatar>
               </div>
            </template>

            <v-list-item-title>
               {{ t("app.profile") }}
            </v-list-item-title>
         </v-list-item>
         <v-list-item link>
            <template v-slot:prepend>
               <v-icon
                  icon="$question"
                  size="default" />
            </template>

            <v-list-item-title>
               {{ t("app.help") }}
            </v-list-item-title>
         </v-list-item>
      </v-list>
   </v-navigation-drawer>

   <v-fade-transition>
      <div
         v-if="expand"
         class="fixed inset-0 z-[1007] bg-black/20"
         @click="expand = false" />
   </v-fade-transition>
</template>

<script lang="ts" setup>
import ToggleIcon from "@/components/Input/ToggleIcon.vue";

const appStore = useAppStore();
const expand = ref(false);
const { t } = useI18n();

const moduleHandler = (value: string) => {
   appStore.setModule(value);
   expand.value = false;
};
</script>
