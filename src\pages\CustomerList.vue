<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
            <RecordButton
               v-bind:disabled="isLoading"
               color="primary"
               @click="recordHandler">
               {{ t("app.add") }}
            </RecordButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="flex-center text-base">{{ t("app.customerList") }}</v-card-title>
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data">
            <template v-slot:item.is_active="{ item }">
               <v-chip v-bind:color="item.is_active ? 'success' : undefined">
                  {{ item.is_active ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>

            <template v-slot:item.is_risky="{ item }">
               <v-chip v-bind:color="item.is_risky ? 'error' : undefined">
                  {{ item.is_risky ? t("app.yes") : t("app.no") }}
               </v-chip>
            </template>

            <template v-slot:item.phone="{ item }">
               {{ item.phone.replace(/(\d{4})(\d{3})(\d{4})/, "$1 $2 $3") }}
            </template>

            <template v-slot:item.actions="{ item }">
               <ActionButton
                  icon="$trash"
                  @click="removeHandler(item)" />
               <ActionButton
                  icon="$edit"
                  @click="recordHandler(item)" />
            </template>
         </DataTable>
      </Card>

      <CustomerDialog ref="dialog" />
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import RecordButton from "@/components/Button/RecordButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ICustomer, useDeleteCustomer, useGetCustomerAll } from "../services/CustomerService";
const CustomerDialog = defineAsyncComponent(() => import("@/components/Dialog/CustomerDialog.vue"));

// hooks
const { t } = useI18n();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const dialog = ref<InstanceType<typeof CustomerDialog>>();
const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<ICustomer>[] => [
   { title: t("app.name"), key: "name", width: "150" },
   { title: t("app.surname"), key: "surname", width: "150" },
   { title: t("app.phone"), key: "phone", width: "150" },
   { title: t("app.email"), key: "email" },
   { title: t("app.currentDebit"), key: "debit", width: "250", money: "₺" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("app.riskStatus"), key: "is_risky", width: "150" },
   { key: "actions", width: "60" }
]);

// services
const { data, isLoading } = useGetCustomerAll();
const deleteCustomer = useDeleteCustomer();

// handlers
const recordHandler = (item?: ICustomer) => {
   dialog.value?.open(item);
};

const removeHandler = async (item: ICustomer) => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteRecord")
      });

      if (confirm) {
         await deleteCustomer.mutateAsync(item.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>
