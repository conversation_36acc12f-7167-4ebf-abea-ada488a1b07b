<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading && isFirst">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isEdit ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <template v-slot:items>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="attr.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="attr.title" />
                     </template>
                  </v-text-field>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useCreateAttr, useGetAttrById, useUpdateAttr } from "../services/AttrService";
import { IAttr, IAttrStore } from "../utils/types";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();

// states
const attr = ref({
   sort_order: 0
} as IAttr);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);
const language = ref(1);

// services
const getAttrById = useGetAttrById({
   enabled: isEdit,
   params: {
      id: routeId,
      language: language
   },
   onSuccess: (data) => {
      attr.value = { ...data };
   }
});
const updateAttr = useUpdateAttr();
const createAttr = useCreateAttr();

// status
const isLoading = computed(() => getAttrById.isLoading.value);
const isFirst = computed(() => getAttrById.isFirst.value);
const isPending = computed(() => createAttr.isPending.value || updateAttr.isPending.value);
const isError = computed(() => getAttrById.isError.value);

// handlers
const formHandler = async () => {
   const payload: IAttrStore = {
      sort_order: attr.value.sort_order,
      translate: [
         {
            language_id: language.value,
            title: attr.value.title
         }
      ]
   };

   try {
      if (isEdit.value) {
         await updateAttr.mutateAsync({ id: attr.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      } else {
         await createAttr.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.attrDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
