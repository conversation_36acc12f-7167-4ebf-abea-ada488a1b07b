import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const assetRoutes: RouteRecordRaw[] = [
   {
      path: "/asset",
      meta: {
         layout: DefaultLayout,
         module: "asset"
      },
      children: [
         {
            path: "product",
            meta: {
               title: i18n.global.t("module.product", 2),
               breadcrumb: i18n.global.t("module.productList")
            },
            children: [
               {
                  path: "",
                  name: "asset.productList",
                  component: getComponent(() => import("../pages/ProductList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.productDetail",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.productCreate",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "category",
            meta: {
               title: i18n.global.t("module.category", 2),
               breadcrumb: i18n.global.t("module.categoryList")
            },
            children: [
               {
                  path: "",
                  name: "asset.categoryList",
                  component: getComponent(() => import("../pages/CategoryList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.categoryDetail",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.categoryCreate",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "manufacturer",
            meta: {
               title: i18n.global.t("module.manufacturer", 2),
               breadcrumb: i18n.global.t("module.manufacturerList")
            },
            children: [
               {
                  path: "",
                  name: "asset.manufacturerList",
                  component: getComponent(() => import("../pages/ManufacturerList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.manufacturerDetail",
                  component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.manufacturerCreate",
                  component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "standard",
            meta: {
               title: i18n.global.t("module.standard", 2),
               breadcrumb: i18n.global.t("module.standardList")
            },
            children: [
               {
                  path: "",
                  name: "asset.standardList",
                  component: getComponent(() => import("../pages/StandardList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.standardDetail",
                  component: getComponent(() => import("../pages/StandardDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.standardCreate",
                  component: getComponent(() => import("../pages/StandardDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "attribute",
            meta: {
               title: i18n.global.t("module.attribute", 2),
               breadcrumb: i18n.global.t("module.attributeList")
            },
            children: [
               {
                  path: "",
                  name: "asset.attrList",
                  component: getComponent(() => import("../pages/AttrList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.attrDetail",
                  component: getComponent(() => import("../pages/AttrDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.attrCreate",
                  component: getComponent(() => import("../pages/AttrDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "competing",
            meta: {
               title: i18n.global.t("module.competing", 2),
               breadcrumb: i18n.global.t("module.competingList")
            },
            children: [
               {
                  path: "",
                  name: "asset.competingList",
                  component: getComponent(() => import("../pages/CompetingList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.competingDetail",
                  component: getComponent(() => import("../pages/CompetingDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.competingCreate",
                  component: getComponent(() => import("../pages/CompetingDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "taxrate",
            meta: {
               title: i18n.global.t("module.taxrate", 2),
               breadcrumb: i18n.global.t("module.taxrateList")
            },
            children: [
               {
                  path: "",
                  name: "asset.taxrateList",
                  component: getComponent(() => import("../pages/TaxrateList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.taxrateDetail",
                  component: getComponent(() => import("../pages/TaxrateDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.taxrateCreate",
                  component: getComponent(() => import("../pages/TaxrateDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         },
         {
            path: "component",
            meta: {
               title: i18n.global.t("module.component", 2),
               breadcrumb: i18n.global.t("module.componentList")
            },
            children: [
               {
                  path: "",
                  name: "asset.componentList",
                  component: getComponent(() => import("../pages/ComponentList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "asset.componentDetail",
                  component: getComponent(() => import("../pages/ComponentDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               },
               {
                  path: "create",
                  name: "asset.componentCreate",
                  component: getComponent(() => import("../pages/ComponentDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.createRecord")
                  }
               }
            ]
         }
      ]
   }
];
