<template>
   <RecordDialog
      ref="recordDialog"
      @after-leave="reset">
      <v-row no-gutters>
         <v-col md="4">
            <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field
               v-model="product.code"
               v-bind:rules="[appRules.required()]"
               v-uppercase />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field
               v-model="product.title"
               v-bind:rules="[appRules.required()]"
               v-ucwords></v-text-field>
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <NumberInput v-model="product.price" />
         </v-col>
         <v-col md="4">
            <v-list-subheader>{{ t("app.stock") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-number-input
               v-model="product.stock"
               v-bind:min="0"
               v-maska="{ mask: 'A', tokens: { A: { pattern: /[0-9]/, repeated: true } } }" />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-switch
               v-model="product.is_active"
               v-bind:false-value="0"
               v-bind:ripple="false"
               v-bind:true-value="1"
               color="primary"
               density="compact">
               <template v-slot:label>
                  <div class="text-sm">{{ product.is_active ? t("app.active") : t("app.passive") }}</div>
               </template>
            </v-switch>
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <SelectInput
               v-model="product.category"
               v-bind:items="categoryAll"
               v-bind:loading="categoryLoading"
               v-bind:open-on-clear="false"
               item-value="id"
               return-object />
         </v-col>
      </v-row>
   </RecordDialog>
</template>

<script lang="ts" setup>
import { useGetCategoryAll } from "@/services/CategoryService";
import type { IProduct, IProductStore } from "@/services/ProductService";
import { useCreateProduct, useGetProductById, useUpdateProduct } from "@/services/ProductService";
import NumberInput from "../Input/NumberInput.vue";
import SelectInput from "../Input/SelectInput.vue";
import RecordDialog from "../Layout/Default/Dialog/RecordDialog.vue";

// hooks
const { t } = useI18n();
const snackbarStore = useSnackbarStore();

// initials
const productInitial = {
   is_active: 1,
   stock: 0
} as IProduct;

// states
const recordDialog = ref<InstanceType<typeof RecordDialog>>();
const product = ref({ ...productInitial });
const dialogId = computed(() => product.value.id);
const enabled = computed(() => !!dialogId.value);

// services
const { isLoading } = useGetProductById({
   id: dialogId,
   enabled: enabled,
   onSuccess: (item) => {
      product.value = { ...item };
   }
});
const createProduct = useCreateProduct();
const updateProduct = useUpdateProduct();

// relation services
const { data: categoryAll, isLoading: categoryLoading } = useGetCategoryAll();

// handlers
const open = async (item?: IProduct, title?: string) => {
   try {
      product.value.id = item?.id || 0;

      if (title) {
         product.value.title = title;
      }

      const confirm = await recordDialog.value?.open({
         width: 800,
         title: item?.id ? t("app.detailRecord") : t("app.createRecord"),
         loading: isLoading
      });

      if (confirm) {
         const payload: IProductStore = {
            ...product.value,
            category_id: product.value.category?.id || null,
            price: product.value.price,
            installment_price: 0
         };

         if (!item?.id) {
            await createProduct.mutateAsync(payload);
            snackbarStore.add({ text: t("app.recordCreated") });
         } else {
            await updateProduct.mutateAsync(payload);
            snackbarStore.add({ text: t("app.recordUpdated") });
         }
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   } finally {
      recordDialog.value?.close();
   }
};

const reset = () => {
   product.value = { ...productInitial };
};

defineExpose({ open, isLoading });
</script>
