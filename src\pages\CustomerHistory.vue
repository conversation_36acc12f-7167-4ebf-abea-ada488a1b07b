<template>
   <Container
      v-bind:error="isError"
      v-bind:loading="isLoading">
      <!-- <PERSON>üşteri Bilgileri Kartı -->
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.customerInfo") }}</v-card-title>
            <v-btn
               v-if="customer"
               class="ml-2"
               icon="$edit"
               size="small"
               variant="text"
               @click="$router.push({ name: 'customerDetail', params: { id: customer.id } })"></v-btn>
         </template>

         <v-card-text v-if="customer">
            <v-row no-gutters>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.name") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1">{{ customer.name }} {{ customer.surname }}</div>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.phone") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1">{{ customer.phone }}</div>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.email") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1">{{ customer.email || "-" }}</div>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.currentDebit") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div
                     class="text-body-1 font-weight-bold"
                     :class="parseFloat(customer.current_debit || '0') > 0 ? 'text-error' : 'text-success'">
                     {{ formatCurrency(customer.current_debit) }}
                  </div>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <!-- Satışlar Kartı -->
      <Card
         v-bind:loading="salesLoading"
         class="mt-4">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.salesHistory") }}</v-card-title>
            <v-btn
               class="ml-2"
               :title="t('app.addSale')"
               color="primary"
               icon="$plus"
               size="small"
               variant="text"
               @click="
                  $router.push({
                     name: 'saleDetail',
                     params: {
                        id: 'create',
                        customer_id: routeId
                     }
                  })
               "></v-btn>
         </template>

         <v-card-text>
            <DataTable
               v-bind:headers="salesHeaders"
               v-bind:items="customerSales"
               v-bind:loading="salesLoading"
               :row-click="(item: ISale) => $router.push({ name: 'saleDetail', params: { id: item.id } })">
               <template v-slot:item.price="{ value }">
                  <div class="font-weight-bold">{{ formatCurrency(value) }}</div>
               </template>
               <template v-slot:item.installment_type="{ value }">
                  <v-chip
                     :color="getInstallmentTypeColor(value)">
                     {{ getInstallmentTypeText(value) }}
                  </v-chip>
               </template>
               <template v-slot:item.created_at="{ value }">
                  <div>{{ formatDate(value) }}</div>
               </template>
            </DataTable>
         </v-card-text>
      </Card>

      <!-- Taksitler Kartı -->
      <Card
         v-bind:loading="installmentsLoading"
         class="mt-4">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.installmentsHistory") }}</v-card-title>
         </template>

         <v-card-text>
            <DataTable
               v-bind:headers="installmentsHeaders"
               v-bind:items="customerInstallments"
               v-bind:loading="installmentsLoading"
               :row-click="(item: any) => $router.push({ name: 'installmentDetail', params: { id: item.id } })">
               <template v-slot:item.amount="{ value }">
                  <div class="font-weight-bold">{{ formatCurrency(value) }}</div>
               </template>
               <template v-slot:item.payment="{ value }">
                  <div class="font-weight-bold text-success">{{ formatCurrency(value) }}</div>
               </template>
               <template v-slot:item.status="{ value, item }">
                  <div class="d-flex align-center gap-2">
                     <v-chip
                        :color="getInstallmentStatusColor(value, item.due_at)">
                        {{ getInstallmentStatusText(value) }}
                     </v-chip>
                     <v-icon
                        v-if="isInstallmentOverdue(value, item.due_at)"
                        color="error"
                        size="small"
                        icon="$alert"
                        :title="`${getOverdueDays(item.due_at)} gün gecikmiş`" />
                  </div>
               </template>
               <template v-slot:item.due_at="{ value, item }">
                  <div
                     :class="{
                        'text-error font-weight-bold': isInstallmentOverdue(item.status, value)
                     }">
                     {{ value ? formatDate(value) : "-" }}
                     <div
                        v-if="isInstallmentOverdue(item.status, value)"
                        class="text-caption text-error">
                        {{ getOverdueDays(value) }} gün gecikmiş
                     </div>
                  </div>
               </template>
               <template v-slot:item.paid_at="{ value }">
                  <div>{{ value ? formatDate(value) : "-" }}</div>
               </template>
               <template v-slot:item.sale_product="{ item }">
                  <div>{{ item.sale_product || "-" }}</div>
               </template>
            </DataTable>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ICustomer, useGetCustomerById, useGetCustomerInstallments, useGetCustomerSales } from "../services/CustomerService";
import { ISale } from "../services/SaleService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();

// customer
const customer = ref<ICustomer | null>(null);
const routeId = computed(() => route.params.id as string);

// set breadcrumb
appStore.setBreadcrumb("CustomerHistory", t("app.customerHistory"));

// services
const getCustomerById = useGetCustomerById({
   id: routeId,
   enabled: computed(() => !!routeId.value),
   onSuccess: (item) => {
      customer.value = { ...item };
   }
});

const getSaleAll = useGetCustomerSales({
   id: routeId,
   enabled: computed(() => !!routeId.value)
});

const getInstallmentAll = useGetCustomerInstallments({
   id: routeId,
   enabled: computed(() => !!routeId.value)
});

// loading states
const isLoading = computed(() => getCustomerById.isLoading.value);
const isError = computed(() => getCustomerById.isError.value);
const salesLoading = computed(() => getSaleAll.isLoading.value);
const installmentsLoading = computed(() => getInstallmentAll.isLoading.value);

const customerSales = computed(() => {
   const salesData = getSaleAll.data.value;
   if (!salesData) return [];
   return Array.isArray(salesData) ? salesData : (salesData.data || []);
});

const customerInstallments = computed(() => {
   const installmentsData = getInstallmentAll.data.value;
   let installments = Array.isArray(installmentsData) ? installmentsData : (installmentsData?.data || []);

   // Taksitleri tarih sıralamasına göre düzenle
   return installments.sort((a: any, b: any) => {
      const today = new Date();
      const dueDateA = a.due_at ? new Date(a.due_at) : new Date(0);
      const dueDateB = b.due_at ? new Date(b.due_at) : new Date(0);

      // Gecikmiş taksitleri önce göster
      const isOverdueA = dueDateA < today && a.status !== 'paid';
      const isOverdueB = dueDateB < today && b.status !== 'paid';

      if (isOverdueA && !isOverdueB) return -1;
      if (!isOverdueA && isOverdueB) return 1;

      // Tarih sıralaması
      return dueDateA.getTime() - dueDateB.getTime();
   });
});

// table headers
const salesHeaders = computed(() => [
   { title: t("app.product"), key: "product", sortable: true },
   { title: t("app.price"), key: "price", sortable: true },
   { title: t("app.installment"), key: "installment", sortable: true },
   { title: t("app.installmentType"), key: "installment_type", sortable: true },
   { title: t("app.createdAt"), key: "created_at", sortable: true }
]);

const installmentsHeaders = computed(() => [
   { title: t("app.product"), key: "sale_product", sortable: false },
   { title: t("app.amount"), key: "amount", sortable: true },
   { title: t("app.payment"), key: "payment", sortable: true },
   { title: t("app.status"), key: "status", sortable: true },
   { title: t("app.dueDate"), key: "due_at", sortable: true },
   { title: t("app.paidDate"), key: "paid_at", sortable: true }
]);

// helper functions
const formatCurrency = (value: string | number): string => {
   const num = typeof value === "string" ? parseFloat(value) || 0 : value;
   return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY"
   }).format(num);
};

const formatDate = (dateString: string | null | undefined): string => {
   if (!dateString) return "-";
   try {
      return new Date(dateString).toLocaleDateString("tr-TR");
   } catch {
      return "-";
   }
};

const getInstallmentTypeColor = (type: string): string => {
   switch (type?.toLowerCase()) {
      case "monthly":
         return "primary";
      case "weekly":
         return "warning";
      case "daily":
         return "info";
      default:
         return "default";
   }
};

const getInstallmentTypeText = (type: string): string => {
   switch (type?.toLowerCase()) {
      case "monthly":
         return t("app.monthly");
      case "weekly":
         return t("app.weekly");
      case "daily":
         return t("app.daily");
      default:
         return type;
   }
};

const getInstallmentStatusColor = (status: string, dueDate?: string): string => {
   const today = new Date();
   const due = dueDate ? new Date(dueDate) : null;
   const isOverdue = due && due < today && status?.toLowerCase() !== 'paid';

   if (isOverdue) return "error";

   switch (status?.toLowerCase()) {
      case "paid":
         return "success";
      case "pending":
         return "warning";
      default:
         return "default";
   }
};

const getOverdueDays = (dueDate: string): number => {
   if (!dueDate) return 0;
   const today = new Date();
   const due = new Date(dueDate);
   const diffTime = today.getTime() - due.getTime();
   const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
   return diffDays > 0 ? diffDays : 0;
};

const isInstallmentOverdue = (status: string, dueDate?: string): boolean => {
   if (!dueDate || status?.toLowerCase() === 'paid') return false;
   const today = new Date();
   const due = new Date(dueDate);
   return due < today;
};

const getInstallmentStatusText = (status: string): string => {
   switch (status?.toLowerCase()) {
      case "paid":
         return t("app.paid");
      case "pending":
         return t("app.pending");
      case "overdue":
         return t("app.overdue");
      default:
         return status;
   }
};
</script>