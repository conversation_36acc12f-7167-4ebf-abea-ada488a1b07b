<?php

declare(strict_types=1);

namespace App\Modules\Asset\Standard;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Standard\StandardRequest;
use App\Modules\Asset\Standard\StandardRepository;

class StandardService extends BaseService {
   /** @var StandardRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      StandardRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createStandard(StandardRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'image_path' => 'nullable',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $id = $this->create([
            'image_path' => $request->image_path,
            'sort_order' => $request->sort_order,
         ]);

         $this->translate($request->translate, [
            'standard_id' => $id
         ], 'asset_standard_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateStandard(StandardRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'image_path' => 'nullable',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $this->update($request, [
            'image_path' => $request->image_path,
            'sort_order' => $request->sort_order,
         ], [
            'id' => $request->id
         ]);

         $this->translate($request->translate, [
            'standard_id' => $request->id
         ], 'asset_standard_translate');

         return $this->getOne($request->id, $lang_id);
      });
   }
}
