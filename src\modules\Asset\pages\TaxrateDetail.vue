<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isEdit ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="taxrate.title"
                     v-bind:rules="[appRules.required()]" />
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.rate") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput
                     v-model="taxrate.rate"
                     v-bind:rules="[appRules.required()]"
                     prepend-inner-icon="$percentage" />
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="taxrate.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ taxrate.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import { useCreateTaxrate, useGetTaxrateById, useUpdateTaxrate } from "../services/TaxrateService";
import { ITaxrate, ITaxrateStore } from "../utils/types";
import NumberInput from "@/components/Input/NumberInput.vue";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();

// states
const taxrate = ref({
   title: "",
   rate: 0
} as ITaxrate);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);

// services
const getTaxrateById = useGetTaxrateById({
   enabled: isEdit,
   params: {
      id: routeId
   },
   onSuccess: (data) => {
      taxrate.value = { ...data };
   }
});
const updateTaxrate = useUpdateTaxrate();
const createTaxrate = useCreateTaxrate();

// status
const isLoading = computed(() => getTaxrateById.isLoading.value);
const isPending = computed(() => createTaxrate.isPending.value || updateTaxrate.isPending.value);
const isError = computed(() => getTaxrateById.isError.value);

// handlers
const formHandler = async () => {
   const payload: ITaxrateStore = {
      title: taxrate.value.title,
      rate: taxrate.value.rate,
      is_active: taxrate.value.is_active,
      sort_order: taxrate.value.sort_order
   };

   try {
      if (isEdit.value) {
         await updateTaxrate.mutateAsync({ id: taxrate.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      } else {
         await createTaxrate.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.taxrateDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
