<?php

declare(strict_types=1);

namespace App\Modules\Asset\Competing;

use App\Core\Abstracts\BaseResource;

class CompetingRequest extends BaseResource {
   public ?int $id;
   public string $title;
   public ?string $content;
   public ?float $price;
   public ?string $currency = 'TRY';
   public ?string $image_path;
   public ?array $competing_category;
   public ?array $competing_manufacturer;
   public ?array $competing_attr;
   public ?array $competing_standard;
   public ?array $competing_product;
}
