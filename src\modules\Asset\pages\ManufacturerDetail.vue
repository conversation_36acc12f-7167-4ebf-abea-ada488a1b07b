<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading && isFirst">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isEdit ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <template v-slot:items>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="manufacturer.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-if="language === 1"
                     v-model="manufacturer.title"
                     v-bind:rules="[appRules.required()]" />
                  <div
                     v-else
                     class="mb-[22px] flex h-9 items-center px-2 text-sm select-all">
                     {{ manufacturer.title }}
                  </div>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="manufacturer.content"
                     v-bind:rules="[appRules.required()]"
                     class="max-grow-80"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="manufacturer.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("module.competingBrand") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="manufacturer.is_competing"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ manufacturer.is_competing ? t("app.yes") : t("app.no") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="manufacturer.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ manufacturer.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="[manufacturer.image_path]" />
                  <ImageUpload v-model="imageUpload" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useCreateManufacturer, useGetManufacturerById, useUpdateManufacturer } from "../services/ManufacturerService";
import { IManufacturer, IManufacturerStore } from "../utils/types";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const manufacturer = ref({
   is_active: 1,
   sort_order: 0,
   is_competing: 0
} as IManufacturer);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);
const language = ref(1);
const imageUpload = ref([] as File[]);

// services
const getManufacturerById = useGetManufacturerById({
   enabled: isEdit,
   params: {
      id: routeId,
      language: language
   },
   onSuccess: (data) => {
      manufacturer.value = { ...data };
   }
});
const updateManufacturer = useUpdateManufacturer();
const createManufacturer = useCreateManufacturer();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["manufacturer", "manufacturerById"] });

// status
const isLoading = computed(() => getManufacturerById.isLoading.value);
const isFirst = computed(() => getManufacturerById.isFirst.value);
const isPending = computed(() => createManufacturer.isPending.value || updateManufacturer.isPending.value);
const isError = computed(() => getManufacturerById.isError.value);

// mutates
const deleteImageMutate = async () => {
   return await deleteImage.mutateAsync({
      id: manufacturer.value.id,
      table: "asset_manufacturer"
   });
};

const uploadImageMutate = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "manufacturer"
   });
};

// handlers
const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteImageMutate();
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: IManufacturerStore = {
      code: manufacturer.value.code,
      title: manufacturer.value.title,
      translate: [
         {
            language_id: language.value,
            content: manufacturer.value.content
         }
      ],
      is_active: manufacturer.value.is_active,
      is_competing: manufacturer.value.is_competing
   };

   try {
      if (imageUpload.value.length) {
         if (manufacturer.value.image_path) {
            await deleteImageMutate();
            snackbarStore.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadImageMutate();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isEdit.value) {
         await updateManufacturer.mutateAsync({ id: manufacturer.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      } else {
         await createManufacturer.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.manufacturerDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
