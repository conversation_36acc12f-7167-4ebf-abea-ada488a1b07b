<template>
   <v-card elevation="2" class="installment-status-chart">
      <v-card-title>
         <div>
            <h3 class="text-h6 font-weight-bold">{{ t("app.installmentStatus") }}</h3>
         </div>
      </v-card-title>

      <v-card-text>
         <div v-if="loading" class="d-flex justify-center align-center" style="height: 300px;">
            <v-progress-circular indeterminate color="primary" />
         </div>

         <div v-else-if="chartData.length === 0" class="d-flex justify-center align-center" style="height: 300px;">
            <div class="text-center">
               <v-icon icon="$pie-chart" size="48" color="grey-lighten-1" class="mb-2" />
               <p class="text-body-2 text-medium-emphasis">{{ t("app.noInstallmentData") }}</p>
            </div>
         </div>

         <div v-else>
            <canvas ref="chartCanvas" style="max-height: 300px;"></canvas>

            <!-- Legend -->
            <div class="mt-4">
               <div class="d-flex flex-wrap justify-center gap-3">
                  <div
                     v-for="(item, index) in chartData"
                     :key="item.label"
                     class="d-flex align-center">
                     <div
                        class="legend-color me-2"
                        :style="{ backgroundColor: colors[index] }"></div>
                     <span class="text-body-2">
                        {{ item.label }} ({{ item.count }})
                     </span>
                  </div>
               </div>
            </div>
         </div>
      </v-card-text>
   </v-card>
</template>

<script lang="ts" setup>
import type { IInstallment } from '@/services/InstallmentService';
import { Chart, registerables } from 'chart.js';
import { toRaw } from 'vue'; // toRaw import et

Chart.register(...registerables);

interface Props {
   installmentsData: IInstallment[];
   loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
   loading: false
});

const { t } = useI18n();
const chartCanvas = ref<HTMLCanvasElement | null>(null);
const chartInstance = ref<Chart | null>(null);

// Chart colors
const colors = [
   'rgb(var(--v-theme-success))',    // Paid
   'rgb(var(--v-theme-warning))',    // Pending
   'rgb(var(--v-theme-error))',      // Overdue
   'rgb(var(--v-theme-info))'        // Partial
];

// Computed chart data
const chartData = computed(() => {
   if (!props.installmentsData || props.installmentsData.length === 0) return [];

   const today = new Date();
   const statusCounts = {
      paid: 0,
      pending: 0,
      overdue: 0,
      partial: 0
   };

   props.installmentsData.forEach(installment => {
      const amount = parseFloat(installment.amount || '0');
      const payment = parseFloat(installment.payment || '0');
      const dueDate = installment.due_at ? new Date(installment.due_at) : null;

      if (payment >= amount) {
         statusCounts.paid++;
      } else if (payment > 0) {
         statusCounts.partial++;
      } else if (dueDate && dueDate < today) {
         statusCounts.overdue++;
      } else {
         statusCounts.pending++;
      }
   });

   const data = [
      {
         label: t('app.paid'),
         value: statusCounts.paid,
         count: statusCounts.paid
      },
      {
         label: t('app.pending'),
         value: statusCounts.pending,
         count: props.installmentsData.filter(installment => {
            const amount = parseFloat(installment.amount || '0');
            const payment = parseFloat(installment.payment || '0');
            const dueDate = installment.due_at ? new Date(installment.due_at) : null;
            return payment < amount && (!dueDate || dueDate >= new Date());
         }).length
      },
      {
         label: t('app.overdue'),
         value: statusCounts.overdue,
         count: statusCounts.overdue
      },
      {
         label: t('app.partial'),
         value: statusCounts.partial,
         count: statusCounts.partial
      }
   ].filter(item => item.value > 0);

   return data;
});

// Chart creation and update
function createChart() {
   if (!chartCanvas.value || chartData.value.length === 0) return;

   const ctx = chartCanvas.value.getContext('2d');
   if (!ctx) return;

   // Destroy existing chart
   if (chartInstance.value) {
      chartInstance.value.destroy();
   }

   // Veriyi deep clone ile tamamen kopyala
   const rawChartData = toRaw(chartData.value);
   const clonedData = structuredClone(rawChartData);

   chartInstance.value = new Chart(ctx, {
      type: 'doughnut',
      data: {
         labels: clonedData.map(item => item.label),
         datasets: [{
            data: clonedData.map(item => item.value),
            backgroundColor: colors.slice(0, clonedData.length),
            borderColor: '#fff',
            borderWidth: 2,
            hoverBorderWidth: 3
         }]
      },
      options: {
         responsive: true,
         maintainAspectRatio: false,
         cutout: '60%',
         plugins: {
            legend: {
               display: false
            },
            tooltip: {
               backgroundColor: 'rgba(0, 0, 0, 0.8)',
               titleColor: '#fff',
               bodyColor: '#fff',
               borderColor: 'rgb(var(--v-theme-primary))',
               borderWidth: 1,
               callbacks: {
                  label: function(context) {
                     const label = context.label || '';
                     const value = context.parsed;
                     const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                     const percentage = ((value / total) * 100).toFixed(1);
                     return `${label}: ${value} (${percentage}%)`;
                  }
               }
            }
         },
         animation: {
            animateRotate: true,
            animateScale: true
         },
         interaction: {
            intersect: false
         }
      }
   });
}

// Watchers
watch([chartData, () => props.loading], () => {
   if (!props.loading) {
      nextTick(() => {
         createChart();
      });
   }
}, { immediate: true });

// Cleanup
onUnmounted(() => {
   if (chartInstance.value) {
      chartInstance.value.destroy();
   }
});
</script>

<style scoped>
.installment-status-chart {
   height: 100%;
}

.legend-color {
   width: 12px;
   height: 12px;
   border-radius: 2px;
   flex-shrink: 0;
}
</style>