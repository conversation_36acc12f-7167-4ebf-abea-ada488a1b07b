<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
         </template>

         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading"
               v-bind:to="{ name: 'asset.standardCreate' }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("module.standardList") }}
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            @row:click="(item) => $router.push({ name: 'asset.standardDetail', params: { id: item.id } })" />
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetStandardAll } from "../services/StandardService";
import { IStandard } from "../utils/types";

// hooks
const { t } = useI18n();

// states
const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IStandard>[] => [
   { title: t("app.title"), key: "title" },
   { title: t("app.createDate"), key: "created_at", width: "250", date: true },
   { title: t("app.updateDate"), key: "updated_at", width: "250", date: true }
]);

// services
const { data, isLoading } = useGetStandardAll();
</script>
