<?php

declare(strict_types=1);

namespace App\Modules\Asset\Taxrate;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Asset\Taxrate\TaxrateRequest;
use App\Modules\Asset\Taxrate\TaxrateRepository;

class TaxrateService extends BaseService {
   /** @var TaxrateRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      TaxrateRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function createTax(TaxrateRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->validate($request->toArray(), [
            'title' => 'required',
            'rate' => 'required|numeric',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric'
         ]);

         $id = $this->create([
            'title' => $request->title,
            'rate' => $request->rate,
            'is_active' => $request->is_active,
            'sort_order' => $request->sort_order
         ]);

         return $this->getOne($id);
      });
   }

   public function updateTax(TaxrateRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'title' => 'required',
            'rate' => 'required|numeric',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric'
         ]);

         $this->update($request, [
            'title' => $request->title,
            'rate' => $request->rate,
            'is_active' => $request->is_active,
            'sort_order' => $request->sort_order
         ], [
            'id' => $request->id
         ]);

         return $this->getOne($request->id);
      });
   }
}
