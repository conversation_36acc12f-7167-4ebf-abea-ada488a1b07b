<?php

declare(strict_types=1);

namespace App\Modules\Asset\Competing;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Competing\CompetingService;
use App\Modules\Asset\Competing\CompetingRequest;
use App\Modules\Asset\Competing\CompetingResponse;

/**
 * @OA\Tag(name="Competing", description="Rakip Ürün işlemleri")
 */
class CompetingController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected CompetingService $service
      ) {
   }

   /**
    * @OA\Get(
    *    tags={"Competing"}, path="/competing/", summary="Rakip Ürün listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllCompeting() {
      $this->response(function () {
         $result = $this->service->getAll($this->language());

         return array_map(function ($item) {
            $response = new CompetingResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Competing"}, path="/competing/{id}", summary="Rakip Ürün detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getCompeting(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language());

         $response = new CompetingResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Post(tags={"Competing"}, path="/competing/", summary="Rakip Ürün ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"title", "content", "price"},
    *       @OA\Property(property="title", type="string", example="Rakip Ürün Başlığı"),
    *       @OA\Property(property="content", type="string", example="Rakip Ürün Açıklaması"),
    *       @OA\Property(property="price", type="number", example=100.00)
    *    ))
    * )
    */
   public function createCompeting() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new CompetingRequest();
         $request->fromArray($json);
         $result = $this->service->createCompeting($request);

         $response = new CompetingResponse();
         $response->fromArray($result);

         return $response;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Competing"}, path="/competing/", summary="Rakip Ürün güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "title", "content", "price"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="title", type="string", example="Rakip Ürün Başlığı"),
    *       @OA\Property(property="content", type="string", example="Rakip Ürün Açıklaması"),
    *       @OA\Property(property="price", type="number", example=100.00)
    *    ))
    * )
    */
   public function updateCompeting() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new CompetingRequest();
         $request->fromArray($json);
         $result = $this->service->updateCompeting($request);

         $response = new CompetingResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Competing"}, path="/competing/{id}", summary="Rakip Ürün sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteCompeting(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'competing_id' => $id
         ]);

         return $result;
      });
   }
}
