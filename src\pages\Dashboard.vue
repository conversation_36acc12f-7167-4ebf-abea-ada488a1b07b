<template>
   <Container>
      <v-row>
         <v-col
            cols="12"
            md="3">
            <v-card
               v-bind:loading="totalSaleLoading"
               class="flex h-full flex-col">
               <v-card-item class="card-content:self-start items-start">
                  <v-card-subtitle class="pb-0">Toplam Satış</v-card-subtitle>
                  <v-card-title>
                     <v-skeleton-loader
                        v-if="totalSaleLoading"
                        type="text"
                        width="50%" />
                     <template v-else>{{ formatNumber(totalSale?.total_sales) }} ₺</template>
                  </v-card-title>
                  <template v-slot:append>
                     <v-avatar
                        color="success"
                        size="56"
                        variant="tonal">
                        <v-icon
                           icon="$try"
                           size="32" />
                     </v-avatar>
                  </template>
               </v-card-item>

               <v-card-actions class="mt-auto">
                  <v-btn
                     density="default"
                     size="small"
                     to="/sale"
                     variant="plain">
                     {{ t("app.showAll") }}
                  </v-btn>
                  <v-spacer />
                  <v-btn-toggle
                     v-model="totalSaleRange"
                     class="h-6"
                     color="primary"
                     divided
                     mandatory
                     variant="outlined">
                     <v-btn
                        value="day"
                        size="small">
                        {{ t("app.daily") }}
                     </v-btn>
                     <v-btn
                        value="week"
                        size="small">
                        {{ t("app.weekly") }}
                     </v-btn>
                     <v-btn
                        value="month"
                        size="small">
                        {{ t("app.monthly") }}
                     </v-btn>
                  </v-btn-toggle>
               </v-card-actions>
            </v-card>
         </v-col>
         <v-col
            cols="12"
            md="3">
            <v-card
               v-bind:loading="activeCustomerLoading"
               class="flex h-full flex-col">
               <v-card-item class="items-start">
                  <v-card-subtitle class="pb-0">Aktif Müşteri</v-card-subtitle>
                  <v-card-title>{{ activeCustomer }}</v-card-title>
                  <template v-slot:append>
                     <v-avatar
                        color="primary"
                        size="56"
                        variant="tonal">
                        <v-icon
                           icon="$user"
                           size="32" />
                     </v-avatar>
                  </template>
               </v-card-item>

               <v-card-actions class="mt-auto">
                  <v-btn
                     density="default"
                     size="small"
                     to="/customer"
                     variant="plain">
                     {{ t("app.showAll") }}
                  </v-btn>
               </v-card-actions>
            </v-card>
         </v-col>
         <v-col
            cols="12"
            md="3">
            <v-card
               v-bind:loading="expectedPaymentsLoading"
               class="flex h-full flex-col">
               <v-card-item class="card-content:self-start items-start">
                  <v-card-subtitle class="pb-0">Beklenen Ödemeler</v-card-subtitle>
                  <v-card-title>
                     <v-skeleton-loader
                        v-if="expectedPaymentsLoading"
                        type="text"
                        width="50%" />
                     <template v-else>{{ formatNumber(expectedPayments?.total_pending_amount) }} ₺</template>
                  </v-card-title>
                  <v-card-subtitle class="pb-0">Geciken Ödemeler</v-card-subtitle>
                  <v-card-title v-if="!expectedPaymentsLoading">{{ formatNumber(expectedPayments?.total_overdue_amount) }} ₺</v-card-title>
                  <template v-slot:append>
                     <v-avatar
                        color="secondary"
                        size="56"
                        variant="tonal">
                        <v-icon
                           icon="$wallet"
                           size="32" />
                     </v-avatar>
                  </template>
               </v-card-item>

               <v-card-actions class="mt-auto">
                  <v-spacer />
                  <v-btn-toggle
                     v-model="expectedPaymentsRange"
                     class="h-6"
                     color="primary"
                     divided
                     mandatory
                     variant="outlined">
                     <v-btn
                        value="week"
                        size="small">
                        {{ t("app.weekly") }}
                     </v-btn>
                     <v-btn
                        value="month"
                        size="small">
                        {{ t("app.monthly") }}
                     </v-btn>
                  </v-btn-toggle>
               </v-card-actions>
            </v-card>
         </v-col>
         <v-col
            cols="12"
            md="3">
            <v-card class="flex h-full flex-col">
               <v-card-item class="items-start pb-0">
                  <v-card-subtitle class="pb-0">Ortalama Taksit</v-card-subtitle>
                  <v-card-title>321.000 ₺</v-card-title>
                  <template v-slot:append>
                     <v-avatar
                        color="info"
                        size="56"
                        variant="tonal">
                        <v-icon
                           icon="$clock"
                           size="32" />
                     </v-avatar>
                  </template>
               </v-card-item>

               <v-card-text class="pb-0">
                  <v-sparkline
                     v-model="value"
                     v-bind:labels="labels"
                     color="primary"
                     height="40"
                     line-width="2" />
               </v-card-text>

               <v-card-actions class="mt-auto">
                  <v-btn-toggle
                     v-model="expectedPaymentsRange"
                     class="h-6"
                     color="primary"
                     divided
                     mandatory
                     variant="outlined">
                     <v-btn
                        value="week"
                        size="small">
                        <
                     </v-btn>
                     <v-btn
                        value="month"
                        size="small">
                        >
                     </v-btn>
                  </v-btn-toggle>
                  <v-spacer />
                  <v-btn-toggle
                     v-model="expectedPaymentsRange"
                     class="h-6"
                     color="primary"
                     divided
                     mandatory
                     variant="outlined">
                     <v-btn
                        value="week"
                        size="small">
                        Miktarsal
                     </v-btn>
                     <v-btn
                        value="month"
                        size="small">
                        Tutarsal
                     </v-btn>
                  </v-btn-toggle>
               </v-card-actions>
            </v-card>
         </v-col>
      </v-row>

      <v-row>
         <v-col
            cols="12"
            md="12">
            <v-card class="flex h-full flex-col">
               <v-card-item class="items-start">
                  <v-card-title class="text-base">Hızlı Erişim</v-card-title>

                  <div class="p-4">
                     <v-row>
                        <v-col
                           cols="12"
                           md="3">
                           <v-btn
                              v-bind:ripple="false"
                              class="h-24 w-full border-2 border-dashed"
                              color="success"
                              variant="outlined"
                              @click="saleDialog?.open()">
                              <div class="flex flex-col items-center font-normal">
                                 <v-icon
                                    class="mb-2"
                                    icon="$plus"
                                    size="32" />
                                 {{ t("app.addSale") }}
                              </div>
                           </v-btn>
                        </v-col>
                        <v-col
                           cols="12"
                           md="3">
                           <v-btn
                              v-bind:ripple="false"
                              class="h-24 w-full border-2 border-dashed"
                              color="primary"
                              variant="outlined"
                              @click="customerDialog?.open()">
                              <div class="flex flex-col items-center font-normal">
                                 <v-icon
                                    class="mb-2"
                                    icon="$addUser"
                                    size="32" />
                                 {{ t("app.addCustomer") }}
                              </div>
                           </v-btn>
                        </v-col>
                        <v-col
                           cols="12"
                           md="3">
                           <v-btn
                              v-bind:ripple="false"
                              class="h-24 w-full border-2 border-dashed"
                              color="info"
                              variant="outlined">
                              <div class="flex flex-col items-center font-normal">
                                 <v-icon
                                    class="mb-2"
                                    icon="$getPayment"
                                    size="32" />
                                 {{ t("app.getPayment") }}
                              </div>
                           </v-btn>
                        </v-col>
                        <v-col
                           cols="12"
                           md="3">
                           <v-btn
                              v-bind:ripple="false"
                              class="h-24 w-full border-2 border-dashed"
                              color="warning"
                              to="/sale"
                              variant="outlined">
                              <div class="flex flex-col items-center font-normal">
                                 <v-icon
                                    class="mb-2"
                                    icon="$sale"
                                    size="32" />
                                 {{ t("app.sale", 2) }}
                              </div>
                           </v-btn>
                        </v-col>
                     </v-row>
                  </div>
               </v-card-item>
            </v-card>
         </v-col>
      </v-row>

      <v-row>
         <v-col
            cols="12"
            md="6">
            <Card>
               <template v-slot:extension>
                  <v-card-title class="text-base">Son Satışlar</v-card-title>
                  <v-spacer />
                  <div class="flex-center pe-2">
                     <SearchInput />
                  </div>
               </template>
               <DataTable
                  v-bind:headers="headers"
                  v-bind:items="items"
                  v-bind:items-per-page="5"
                  v-bind:loading="loading"
                  v-bind:sticky-footer="false"
                  v-bind:sticky-header="false" />
            </Card>
         </v-col>
         <v-col
            cols="12"
            md="6">
            <Card>
               <template v-slot:extension>
                  <v-card-title class="text-base">Yaklaşan Ödemeler</v-card-title>
                  <v-spacer />
                  <div class="flex-center pe-2">
                     <SearchInput />
                  </div>
               </template>
               <DataTable
                  v-bind:headers="headers"
                  v-bind:items="items"
                  v-bind:items-per-page="5"
                  v-bind:loading="loading"
                  v-bind:sticky-footer="false"
                  v-bind:sticky-header="false" />
            </Card>
         </v-col>
      </v-row>

      <SaleDialog ref="saleDialog" />
      <CustomerDialog ref="customerDialog" />
   </Container>
</template>

<script lang="ts" setup>
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetDashboardActiveCustomer, useGetDashboardExpectedPayments, useGetDashboardTotalSale } from "@/services/DashboardService";
const SaleDialog = defineAsyncComponent(() => import("@/components/Dialog/SaleDialog.vue"));
const CustomerDialog = defineAsyncComponent(() => import("@/components/Dialog/CustomerDialog.vue"));

// hooks
const { t } = useI18n();

// const labels = ref(["Oca", "Şub", "Mar", "Nis", "May", "Haz", "Tem", "Ağu", "Eyl"]);
// const value = ref([100, 675, 410, 390, 310, 460, 250, 240, 100]);
const labels = ref(["Oca", "Şub", "Mar", "Nis", "May", "Haz", "Tem", "Ağu", "Eyl", "Ekim", "Kas", "Ara"]);
const value = ref([100, 675, 410, 390, 310, 460, 250, 240, 100, 100, 100, 100]);

const headers = ref([
   { title: "Müşteri", key: "customer" },
   { title: "Tutar", key: "amount" },
   { title: "Tarih", key: "date" }
]);

const items = ref([
   { customer: "Müşteri 1", amount: "100 ₺", date: "1 Ocak 2021" },
   { customer: "Müşteri 2", amount: "200 ₺", date: "2 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" },
   { customer: "Müşteri 3", amount: "300 ₺", date: "3 Ocak 2021" }
]);

const loading = ref(false);

const saleDialog = ref<InstanceType<typeof SaleDialog>>();
const customerDialog = ref<InstanceType<typeof CustomerDialog>>();

const date = useDate();
const endOfDay = computed(() => date.endOfDay(new Date())) as ComputedRef<Date>;
const totalSaleRange = ref<"day" | "week" | "month">("week");
const expectedPaymentsRange = ref<"week" | "month">("week");

const totalSaleStartDate = computed(() => {
   switch (totalSaleRange.value) {
      case "day":
         return date.startOfDay(endOfDay.value);
      case "week":
         return date.startOfWeek(endOfDay.value);
      case "month":
      default:
         return date.startOfMonth(endOfDay.value);
   }
}) as ComputedRef<Date>;
const expectedPaymentsStartDate = computed(() =>
   expectedPaymentsRange.value === "week" ? date.startOfWeek(endOfDay.value) : date.startOfMonth(endOfDay.value)
) as ComputedRef<Date>;
const expectedPaymentsEnd = computed(() => (expectedPaymentsRange.value === "week" ? date.endOfWeek(endOfDay.value) : date.endOfMonth(endOfDay.value))) as ComputedRef<Date>;
// services
const { data: totalSale, isLoading: totalSaleLoading } = useGetDashboardTotalSale({
   params: {
      start_date: totalSaleStartDate,
      end_date: endOfDay
   }
});

const { data: activeCustomer, isLoading: activeCustomerLoading } = useGetDashboardActiveCustomer();

const { data: expectedPayments, isLoading: expectedPaymentsLoading } = useGetDashboardExpectedPayments({
   params: {
      start_date: expectedPaymentsStartDate,
      end_date: expectedPaymentsEnd
   }
});
</script>
