import { ICompeting, ICompetingStore } from "../utils/types";

export const useGetCompetingAll = (payload?: TQuery<ICompeting[]>) => {
   const options = computed(() => ({
      queryKey: ["competing", "competingAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/competing/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCompetingById = (payload?: TQuery<ICompeting>) => {
   const options = computed(() => ({
      queryKey: ["competing", "competingById", payload?.params?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/competing/${toValue(payload?.params?.id)}`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateCompeting = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["competing", "updateCompeting"],
      mutationFn: async (data: ICompetingStore): Promise<TResponse<ICompeting>> => {
         return (await appAxios.put("/asset/competing/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["competing"] });
      }
   });
};

export const useCreateCompeting = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["competing", "createCompeting"],
      mutationFn: async (data: ICompetingStore): Promise<TResponse<ICompeting>> => {
         return (await appAxios.post("/asset/competing/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["competing"] });
      }
   });
};

export const useDeleteCompeting = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["competing", "deleteCompeting"],
      mutationFn: async (id: number): Promise<TResponse<ICompeting>> => {
         return (await appAxios.delete(`/asset/competing/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["competing"] });
      }
   });
};
