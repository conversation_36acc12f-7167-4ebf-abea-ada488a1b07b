/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EButton: typeof import('./src/utils/enums')['EButton']
  const ELanguage: typeof import('./src/utils/enums')['ELanguage']
  const ENotify: typeof import('./src/utils/enums')['ENotify']
  const EUser: typeof import('./src/utils/enums')['EUser']
  const EffectScope: typeof import('vue')['EffectScope']
  const TDialog: typeof import('./src/utils/types')['TDialog']
  const VITE_MEDIA: typeof import('./src/utils/helper')['VITE_MEDIA']
  const VueQueryPlugin: typeof import('@tanstack/vue-query')['VueQueryPlugin']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const appAxios: typeof import('./src/utils/axios')['appAxios']
  const appConfig: typeof import('./src/utils/config')['appConfig']
  const appMenu: typeof import('./src/utils/menu')['appMenu']
  const appRules: typeof import('./src/utils/rules')['appRules']
  const attemptDelay: typeof import('./src/utils/helper')['attemptDelay']
  const axios: typeof import('axios')['default']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createFormData: typeof import('./src/utils/helper')['createFormData']
  const createI18n: typeof import('vue-i18n')['createI18n']
  const createPinia: typeof import('pinia')['createPinia']
  const createRouter: typeof import('vue-router')['createRouter']
  const createSlug: typeof import('./src/utils/helper')['createSlug']
  const createVuetify: typeof import('vuetify')['createVuetify']
  const createWebHistory: typeof import('vue-router')['createWebHistory']
  const customRef: typeof import('vue')['customRef']
  const debounceTimer: typeof import('./src/utils/helper')['debounceTimer']
  const deepValue: typeof import('./src/utils/helper')['deepValue']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const dragHide: typeof import('./src/utils/helper')['dragHide']
  const effectScope: typeof import('vue')['effectScope']
  const escapeUrl: typeof import('./src/utils/helper')['escapeUrl']
  const fileInfo: typeof import('./src/utils/helper')['fileInfo']
  const formatCounter: typeof import('./src/utils/helper')['formatCounter']
  const formatDate: typeof import('./src/utils/helper')['formatDate']
  const formatMS: typeof import('./src/utils/helper')['formatMS']
  const formatNumber: typeof import('./src/utils/helper')['formatNumber']
  const formatSize: typeof import('./src/utils/helper')['formatSize']
  const generateRandomGuid: typeof import('./src/utils/helper')['generateRandomGuid']
  const generateRandomString: typeof import('./src/utils/helper')['generateRandomString']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getComponent: typeof import('./src/utils/helper')['getComponent']
  const getComponentAsync: typeof import('./src/utils/helper')['getComponentAsync']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getCurrentWatcher: typeof import('vue')['getCurrentWatcher']
  const getLocale: typeof import('./src/utils/helper')['getLocale']
  const getProvider: typeof import('./src/utils/helper')['getProvider']
  const getTheme: typeof import('./src/utils/helper')['getTheme']
  const h: typeof import('vue')['h']
  const i18n: typeof import('./src/utils/i18n')['i18n']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const loadLocale: typeof import('./src/utils/helper')['loadLocale']
  const loadMenu: typeof import('./src/utils/helper')['loadMenu']
  const loadRoute: typeof import('./src/utils/helper')['loadRoute']
  const lowerCase: typeof import('./src/utils/helper')['lowerCase']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const mat: typeof import('./src/utils/icons-material')['mat']
  const matAliases: typeof import('./src/utils/icons-material')['matAliases']
  const mergeProps: typeof import('vue')['mergeProps']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const parseNumber: typeof import('./src/utils/helper')['parseNumber']
  const persistedstate: typeof import('pinia-plugin-persistedstate')['default']
  const pho: typeof import('./src/utils/icons-phosphor')['pho']
  const phoAliases: typeof import('./src/utils/icons-phosphor')['phoAliases']
  const pinia: typeof import('./src/utils/pinia')['pinia']
  const position: typeof import('./src/utils/helper')['position']
  const prefixNumber: typeof import('./src/utils/helper')['prefixNumber']
  const provide: typeof import('vue')['provide']
  const query: typeof import('./src/utils/query')['query']
  const queryData: typeof import('./src/utils/helper')['queryData']
  const queryOptions: typeof import('./src/utils/query')['queryOptions']
  const queryPrefetch: typeof import('./src/utils/helper')['queryPrefetch']
  const queryWrapper: typeof import('./src/utils/helper')['queryWrapper']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const registerDirectives: typeof import('./src/utils/directive')['registerDirectives']
  const registerI18n: typeof import('./src/utils/i18n')['registerI18n']
  const registerMenu: typeof import('./src/utils/menu')['registerMenu']
  const registerProviders: typeof import('./src/utils/provider')['registerProviders']
  const registerRoutes: typeof import('./src/utils/router')['registerRoutes']
  const replaceString: typeof import('./src/utils/helper')['replaceString']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const router: typeof import('./src/utils/router')['router']
  const searchString: typeof import('./src/utils/helper')['searchString']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setInitialData: typeof import('./src/utils/helper')['setInitialData']
  const setLocale: typeof import('./src/utils/helper')['setLocale']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const setProvider: typeof import('./src/utils/helper')['setProvider']
  const setTheme: typeof import('./src/utils/helper')['setTheme']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const sleepDelay: typeof import('./src/utils/helper')['sleepDelay']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const suffixNumber: typeof import('./src/utils/helper')['suffixNumber']
  const tabler: typeof import('./src/utils/icons-tabler')['tabler']
  const tablerAliases: typeof import('./src/utils/icons-tabler')['tablerAliases']
  const tailwind3: typeof import('./src/utils/tailwind3')['default']
  const throttleTimer: typeof import('./src/utils/helper')['throttleTimer']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const toggleTheme: typeof import('./src/utils/helper')['toggleTheme']
  const triggerRef: typeof import('vue')['triggerRef']
  const ucFirst: typeof import('./src/utils/helper')['ucFirst']
  const ucWords: typeof import('./src/utils/helper')['ucWords']
  const unref: typeof import('vue')['unref']
  const upperCase: typeof import('./src/utils/helper')['upperCase']
  const useAppStore: typeof import('./src/stores/appStore')['useAppStore']
  const useAttrs: typeof import('vue')['useAttrs']
  const useAuthStore: typeof import('./src/stores/authStore')['useAuthStore']
  const useConfirmStore: typeof import('./src/stores/confirmStore')['useConfirmStore']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDate: typeof import('vuetify')['useDate']
  const useI18n: typeof import('vue-i18n')['useI18n']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useMutation: typeof import('@tanstack/vue-query')['useMutation']
  const useNotifyStore: typeof import('./src/stores/notifyStore')['useNotifyStore']
  const usePromptStore: typeof import('./src/stores/promptStore')['usePromptStore']
  const useQuery: typeof import('@tanstack/vue-query')['useQuery']
  const useQueryClient: typeof import('@tanstack/vue-query')['useQueryClient']
  const useReactiveStore: typeof import('./src/stores/reactiveStore')['useReactiveStore']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useSnackbarStore: typeof import('./src/stores/messageStore')['useSnackbarStore']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTheme: typeof import('vuetify')['useTheme']
  const useTranslate: typeof import('./src/utils/helper')['useTranslate']
  const vuetify: typeof import('./src/utils/vuetify')['vuetify']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, ShallowRef, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef, App } from 'vue'
  import('vue')
  // @ts-ignore
  export type { RouteLocation, RouteRecordRaw, Router, RouteMeta, RouteLocationNormalizedLoaded } from 'vue-router'
  import('vue-router')
  // @ts-ignore
  export type { VueQueryPluginOptions, UseQueryReturnType, UseQueryOptions } from '@tanstack/vue-query'
  import('@tanstack/vue-query')
  // @ts-ignore
  export type { ThemeInstance, IconProps } from 'vuetify'
  import('vuetify')
  // @ts-ignore
  export type { TResponse } from './src/utils/axios'
  import('./src/utils/axios')
  // @ts-ignore
  export type { EButton, ENotify, EUser, ELanguage } from './src/utils/enums'
  import('./src/utils/enums')
  // @ts-ignore
  export type { TQuery, TMutation } from './src/utils/query'
  import('./src/utils/query')
  // @ts-ignore
  export type { TRoute } from './src/utils/router'
  import('./src/utils/router')
  // @ts-ignore
  export type { IListImage, IDefaultFields, ITranslate, THeader, TNotify, TSnackbar } from './src/utils/types'
  import('./src/utils/types')
  // @ts-ignore
  export type { TList, TDataTable, TMultiSelect, TDateField, TToolbar, TCard, TContainer, TBtn, TField } from './src/utils/vuetify'
  import('./src/utils/vuetify')
}

// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    readonly EButton: UnwrapRef<typeof import('./src/utils/enums')['EButton']>
    readonly ELanguage: UnwrapRef<typeof import('./src/utils/enums')['ELanguage']>
    readonly ENotify: UnwrapRef<typeof import('./src/utils/enums')['ENotify']>
    readonly EUser: UnwrapRef<typeof import('./src/utils/enums')['EUser']>
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly TDialog: UnwrapRef<typeof import('./src/utils/types')['TDialog']>
    readonly VITE_MEDIA: UnwrapRef<typeof import('./src/utils/helper')['VITE_MEDIA']>
    readonly VueQueryPlugin: UnwrapRef<typeof import('@tanstack/vue-query')['VueQueryPlugin']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('pinia')['acceptHMRUpdate']>
    readonly appAxios: UnwrapRef<typeof import('./src/utils/axios')['appAxios']>
    readonly appConfig: UnwrapRef<typeof import('./src/utils/config')['appConfig']>
    readonly appMenu: UnwrapRef<typeof import('./src/utils/menu')['appMenu']>
    readonly appRules: UnwrapRef<typeof import('./src/utils/rules')['appRules']>
    readonly attemptDelay: UnwrapRef<typeof import('./src/utils/helper')['attemptDelay']>
    readonly axios: UnwrapRef<typeof import('axios')['default']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createFormData: UnwrapRef<typeof import('./src/utils/helper')['createFormData']>
    readonly createI18n: UnwrapRef<typeof import('vue-i18n')['createI18n']>
    readonly createPinia: UnwrapRef<typeof import('pinia')['createPinia']>
    readonly createRouter: UnwrapRef<typeof import('vue-router')['createRouter']>
    readonly createSlug: UnwrapRef<typeof import('./src/utils/helper')['createSlug']>
    readonly createVuetify: UnwrapRef<typeof import('vuetify')['createVuetify']>
    readonly createWebHistory: UnwrapRef<typeof import('vue-router')['createWebHistory']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly debounceTimer: UnwrapRef<typeof import('./src/utils/helper')['debounceTimer']>
    readonly deepValue: UnwrapRef<typeof import('./src/utils/helper')['deepValue']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineStore: UnwrapRef<typeof import('pinia')['defineStore']>
    readonly dragHide: UnwrapRef<typeof import('./src/utils/helper')['dragHide']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly escapeUrl: UnwrapRef<typeof import('./src/utils/helper')['escapeUrl']>
    readonly fileInfo: UnwrapRef<typeof import('./src/utils/helper')['fileInfo']>
    readonly formatCounter: UnwrapRef<typeof import('./src/utils/helper')['formatCounter']>
    readonly formatDate: UnwrapRef<typeof import('./src/utils/helper')['formatDate']>
    readonly formatMS: UnwrapRef<typeof import('./src/utils/helper')['formatMS']>
    readonly formatNumber: UnwrapRef<typeof import('./src/utils/helper')['formatNumber']>
    readonly formatSize: UnwrapRef<typeof import('./src/utils/helper')['formatSize']>
    readonly generateRandomGuid: UnwrapRef<typeof import('./src/utils/helper')['generateRandomGuid']>
    readonly generateRandomString: UnwrapRef<typeof import('./src/utils/helper')['generateRandomString']>
    readonly getActivePinia: UnwrapRef<typeof import('pinia')['getActivePinia']>
    readonly getComponent: UnwrapRef<typeof import('./src/utils/helper')['getComponent']>
    readonly getComponentAsync: UnwrapRef<typeof import('./src/utils/helper')['getComponentAsync']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getCurrentWatcher: UnwrapRef<typeof import('vue')['getCurrentWatcher']>
    readonly getLocale: UnwrapRef<typeof import('./src/utils/helper')['getLocale']>
    readonly getProvider: UnwrapRef<typeof import('./src/utils/helper')['getProvider']>
    readonly getTheme: UnwrapRef<typeof import('./src/utils/helper')['getTheme']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly i18n: UnwrapRef<typeof import('./src/utils/i18n')['i18n']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly loadLocale: UnwrapRef<typeof import('./src/utils/helper')['loadLocale']>
    readonly loadMenu: UnwrapRef<typeof import('./src/utils/helper')['loadMenu']>
    readonly loadRoute: UnwrapRef<typeof import('./src/utils/helper')['loadRoute']>
    readonly lowerCase: UnwrapRef<typeof import('./src/utils/helper')['lowerCase']>
    readonly mapActions: UnwrapRef<typeof import('pinia')['mapActions']>
    readonly mapGetters: UnwrapRef<typeof import('pinia')['mapGetters']>
    readonly mapState: UnwrapRef<typeof import('pinia')['mapState']>
    readonly mapStores: UnwrapRef<typeof import('pinia')['mapStores']>
    readonly mapWritableState: UnwrapRef<typeof import('pinia')['mapWritableState']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mat: UnwrapRef<typeof import('./src/utils/icons-material')['mat']>
    readonly matAliases: UnwrapRef<typeof import('./src/utils/icons-material')['matAliases']>
    readonly mergeProps: UnwrapRef<typeof import('vue')['mergeProps']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly parseNumber: UnwrapRef<typeof import('./src/utils/helper')['parseNumber']>
    readonly persistedstate: UnwrapRef<typeof import('pinia-plugin-persistedstate')['default']>
    readonly pho: UnwrapRef<typeof import('./src/utils/icons-phosphor')['pho']>
    readonly phoAliases: UnwrapRef<typeof import('./src/utils/icons-phosphor')['phoAliases']>
    readonly pinia: UnwrapRef<typeof import('./src/utils/pinia')['pinia']>
    readonly position: UnwrapRef<typeof import('./src/utils/helper')['position']>
    readonly prefixNumber: UnwrapRef<typeof import('./src/utils/helper')['prefixNumber']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly query: UnwrapRef<typeof import('./src/utils/query')['query']>
    readonly queryData: UnwrapRef<typeof import('./src/utils/helper')['queryData']>
    readonly queryOptions: UnwrapRef<typeof import('./src/utils/query')['queryOptions']>
    readonly queryPrefetch: UnwrapRef<typeof import('./src/utils/helper')['queryPrefetch']>
    readonly queryWrapper: UnwrapRef<typeof import('./src/utils/helper')['queryWrapper']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly registerDirectives: UnwrapRef<typeof import('./src/utils/directive')['registerDirectives']>
    readonly registerI18n: UnwrapRef<typeof import('./src/utils/i18n')['registerI18n']>
    readonly registerMenu: UnwrapRef<typeof import('./src/utils/menu')['registerMenu']>
    readonly registerProviders: UnwrapRef<typeof import('./src/utils/provider')['registerProviders']>
    readonly registerRoutes: UnwrapRef<typeof import('./src/utils/router')['registerRoutes']>
    readonly replaceString: UnwrapRef<typeof import('./src/utils/helper')['replaceString']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly router: UnwrapRef<typeof import('./src/utils/router')['router']>
    readonly searchString: UnwrapRef<typeof import('./src/utils/helper')['searchString']>
    readonly setActivePinia: UnwrapRef<typeof import('pinia')['setActivePinia']>
    readonly setInitialData: UnwrapRef<typeof import('./src/utils/helper')['setInitialData']>
    readonly setLocale: UnwrapRef<typeof import('./src/utils/helper')['setLocale']>
    readonly setMapStoreSuffix: UnwrapRef<typeof import('pinia')['setMapStoreSuffix']>
    readonly setProvider: UnwrapRef<typeof import('./src/utils/helper')['setProvider']>
    readonly setTheme: UnwrapRef<typeof import('./src/utils/helper')['setTheme']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly sleepDelay: UnwrapRef<typeof import('./src/utils/helper')['sleepDelay']>
    readonly storeToRefs: UnwrapRef<typeof import('pinia')['storeToRefs']>
    readonly suffixNumber: UnwrapRef<typeof import('./src/utils/helper')['suffixNumber']>
    readonly tabler: UnwrapRef<typeof import('./src/utils/icons-tabler')['tabler']>
    readonly tablerAliases: UnwrapRef<typeof import('./src/utils/icons-tabler')['tablerAliases']>
    readonly tailwind3: UnwrapRef<typeof import('./src/utils/tailwind3')['default']>
    readonly throttleTimer: UnwrapRef<typeof import('./src/utils/helper')['throttleTimer']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly toggleTheme: UnwrapRef<typeof import('./src/utils/helper')['toggleTheme']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly ucFirst: UnwrapRef<typeof import('./src/utils/helper')['ucFirst']>
    readonly ucWords: UnwrapRef<typeof import('./src/utils/helper')['ucWords']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly upperCase: UnwrapRef<typeof import('./src/utils/helper')['upperCase']>
    readonly useAppStore: UnwrapRef<typeof import('./src/stores/appStore')['useAppStore']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useAuthStore: UnwrapRef<typeof import('./src/stores/authStore')['useAuthStore']>
    readonly useConfirmStore: UnwrapRef<typeof import('./src/stores/confirmStore')['useConfirmStore']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useDate: UnwrapRef<typeof import('vuetify')['useDate']>
    readonly useI18n: UnwrapRef<typeof import('vue-i18n')['useI18n']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useMutation: UnwrapRef<typeof import('@tanstack/vue-query')['useMutation']>
    readonly useNotifyStore: UnwrapRef<typeof import('./src/stores/notifyStore')['useNotifyStore']>
    readonly usePromptStore: UnwrapRef<typeof import('./src/stores/promptStore')['usePromptStore']>
    readonly useQuery: UnwrapRef<typeof import('@tanstack/vue-query')['useQuery']>
    readonly useQueryClient: UnwrapRef<typeof import('@tanstack/vue-query')['useQueryClient']>
    readonly useReactiveStore: UnwrapRef<typeof import('./src/stores/reactiveStore')['useReactiveStore']>
    readonly useRoute: UnwrapRef<typeof import('vue-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('vue-router')['useRouter']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useSnackbarStore: UnwrapRef<typeof import('./src/stores/messageStore')['useSnackbarStore']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useTheme: UnwrapRef<typeof import('vuetify')['useTheme']>
    readonly useTranslate: UnwrapRef<typeof import('./src/utils/helper')['useTranslate']>
    readonly vuetify: UnwrapRef<typeof import('./src/utils/vuetify')['vuetify']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}