export const productMenu: TList[] = [
   {
      itemType: "subheader",
      itemTitle: i18n.global.t("module.website")
   },
   {
      itemType: "divider"
   },
   {
      itemTitle: i18n.global.t("module.product"),
      itemProps: {
         to: "/website/product",
         prependIcon: "$product"
      }
   },
   {
      itemTitle: i18n.global.t("module.definition"),
      itemProps: {
         prependIcon: "$definitions",
         value: "website-definitions"
      },
      children: [
         {
            itemTitle: i18n.global.t("module.category"),
            itemProps: {
               to: "/website/category"
            }
         },
         {
            itemTitle: i18n.global.t("module.manufacturer"),
            itemProps: {
               to: "/website/manufacturer"
            }
         }
      ]
   }
];
