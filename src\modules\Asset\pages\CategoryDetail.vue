<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading && isFirst">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isEdit ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <template v-slot:items>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="category.content"
                     v-bind:rules="[appRules.required()]"
                     class="max-grow-80"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="category.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ category.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="[category.image_path]" />
                  <ImageUpload v-model="imageUpload" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useCreateCategory, useGetCategoryById, useUpdateCategory } from "../services/CategoryService";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { ICategory, ICategoryStore } from "../utils/types";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const category = ref({
   is_active: 1,
   sort_order: 0,
   parent_id: 0,
   group_id: 0
} as ICategory);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);
const language = ref(1);
const imageUpload = ref([] as File[]);

// services
const getCategoryById = useGetCategoryById({
   enabled: isEdit,
   params: {
      id: routeId,
      language: language
   },
   onSuccess: (data) => {
      category.value = { ...data };
   }
});
const updateCategory = useUpdateCategory();
const createCategory = useCreateCategory();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["category", "categoryById"] });

// status
const isLoading = computed(() => getCategoryById.isLoading.value);
const isFirst = computed(() => getCategoryById.isFirst.value);
const isPending = computed(() => createCategory.isPending.value || updateCategory.isPending.value);
const isError = computed(() => getCategoryById.isError.value);

// mutates
const deleteImageMutate = async () => {
   return await deleteImage.mutateAsync({
      id: category.value.id,
      table: "asset_category"
   });
};

const uploadImageMutate = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "category"
   });
};

// handlers
const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteImageMutate();
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: ICategoryStore = {
      code: category.value.code,
      is_active: category.value.is_active,
      sort_order: category.value.sort_order,
      parent_id: category.value.parent_id,
      group_id: category.value.group_id,
      translate: [
         {
            language_id: language.value,
            title: category.value.title,
            content: category.value.content
         }
      ]
   };

   try {
      if (imageUpload.value.length) {
         if (category.value.image_path) {
            await deleteImageMutate();
            snackbarStore.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadImageMutate();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isEdit.value) {
         await updateCategory.mutateAsync({ id: category.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      } else {
         await createCategory.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.categoryDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
