<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
            <RecordButton
               v-bind:disabled="isLoading"
               color="primary"
               @click="recordHandler">
               {{ t("app.add") }}
            </RecordButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="flex-center text-base">{{ t("app.saleList") }}</v-card-title>
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            expand-on-click
            @row:expand="(item) => expandHandler(item)">
            <template v-slot:item.customer.phone="{ item }">
               {{ item.customer?.phone?.replace(/(\d{4})(\d{3})(\d{4})/, "$1 $2 $3") }}
            </template>

            <template v-slot:item.installment_type="{ item }">
               <v-chip color="primary">
                  {{ item.installment_type === "monthly" ? t("app.monthly") : item.installment_type === "weekly" ? t("app.weekly") : item.installment_type }}
               </v-chip>
            </template>

            <template v-slot:item.actions="{ item }">
               <ActionButton
                  v-bind:to="{ name: 'saleDetail', params: { id: item.id } }"
                  icon="$edit" />
            </template>

            <template v-slot:expand="{ item }">
               <v-row class="m-4">
                  <v-col
                     cols="12"
                     xl="6">
                     <v-list-subheader>{{ t("app.productList") }}</v-list-subheader>
                     <div class="border-thin overflow-hidden rounded-md">
                        <DataTable
                           v-bind:headers="productHeaders"
                           v-bind:items="expandedItems[item.id]?.data?.product_list"
                           v-bind:items-per-page="-1"
                           v-bind:loading="expandedItems[item.id]?.isLoading"
                           v-bind:sticky-header="false"
                           accent-header
                           disable-sort
                           hide-default-footer
                           skeleton="list-item-three-line">
                           <template v-slot:item.total="{ item }">{{ formatNumber(Number(item.price) * item.quantity) }} ₺</template>

                           <template v-slot:append>
                              <tr>
                                 <td colspan="3">
                                    {{ t("app.total") }}
                                 </td>
                                 <td class="font-semibold">
                                    {{ expandedItems[item.id]?.data?.product_list.reduce((sum, product) => sum + product.quantity, 0) }}
                                 </td>
                                 <td class="text-right font-semibold">
                                    {{ formatNumber(expandedItems[item.id]?.data?.product_list.reduce((sum, product) => sum + Number(product.price) * product.quantity, 0)) }} ₺
                                 </td>
                              </tr>
                           </template>
                        </DataTable>
                     </div>
                  </v-col>

                  <v-col
                     cols="12"
                     xl="6">
                     <v-list-subheader>{{ t("app.installmentList") }}</v-list-subheader>
                     <div class="border-thin overflow-hidden rounded-md">
                        <DataTable
                           v-bind:headers="installmentHeaders"
                           v-bind:items="expandedItems[item.id]?.data?.installment_list"
                           v-bind:items-per-page="-1"
                           v-bind:loading="expandedItems[item.id]?.isLoading"
                           v-bind:sticky-footer="false"
                           v-bind:sticky-header="false"
                           accent-header
                           disable-sort
                           hide-default-footer>
                           <template v-slot:item.status="{ item }">
                              <v-chip v-bind:color="{ paid: 'primary', pending: 'undefined', overdue: 'error' }[item.status]">
                                 {{ item.status }}
                              </v-chip>
                           </template>

                           <template v-slot:item.balance="{ item }">{{ formatNumber(Number(item.amount) - Number(item.payment)) }} ₺</template>

                           <template v-slot:append>
                              <tr>
                                 <td colspan="4">
                                    {{ t("app.total") }}
                                 </td>
                                 <td class="text-right font-semibold">
                                    {{
                                       formatNumber(
                                          expandedItems[item.id]?.data?.installment_list.reduce(
                                             (sum, installment) => sum + (Number(installment.amount) - Number(installment.payment)),
                                             0
                                          )
                                       )
                                    }}
                                    ₺
                                 </td>
                                 <td></td>
                              </tr>
                           </template>
                        </DataTable>
                     </div>
                  </v-col>
               </v-row>
            </template>
         </DataTable>
      </Card>

      <SaleDialog ref="dialog" />
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import RecordButton from "@/components/Button/RecordButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ISale, ISaleInstallment, ISaleProduct, useGetSaleAll, useGetSaleById } from "../services/SaleService";
const SaleDialog = defineAsyncComponent(() => import("@/components/Dialog/SaleDialog.vue"));

// hooks
const { t } = useI18n();

// states
const dialog = ref<InstanceType<typeof SaleDialog>>();
const expandedId = ref();
const expandedItems = ref<{ [key: string]: { data?: ISale; isLoading: boolean } }>({});
const enabled = computed(() => !!expandedId.value);
const filter = ref();
const selected = ref([]);
const headers = computed((): THeader<ISale>[] => [
   { title: t("app.saleDate"), key: "sale_date", width: "200", date: true },
   { title: t("app.name"), key: "customer.name", width: "150" },
   { title: t("app.surname"), key: "customer.surname", width: "150" },
   { title: t("app.phone"), key: "customer.phone", width: "150" },
   { title: t("app.email"), key: "customer.email" },
   { title: t("app.price"), key: "total_amount", width: "150", money: "₺" },
   { title: t("app.installment"), key: "installment", width: "100" },
   { title: t("app.installmentType"), key: "installment_type" },
   { key: "actions" }
]);

const productHeaders = computed((): THeader<ISaleProduct & { total: number }>[] => [
   { title: t("app.code"), key: "code", width: "100" },
   { title: t("app.title"), key: "title" },
   { title: t("app.price"), key: "price", width: "150", money: "₺" },
   { title: t("app.quantity"), key: "quantity", width: "100" },
   { title: t("app.total"), key: "total", width: "200", align: "end" }
]);

const installmentHeaders = computed((): THeader<ISaleInstallment & { balance: number }>[] => [
   { title: t("app.installmentDate"), key: "due_at", width: "150", date: true },
   { title: t("app.installmentPrice"), key: "amount", width: "150", money: "₺" },
   { title: t("app.status"), key: "status", width: "150" },
   { title: t("app.paidDate"), key: "paid_at", width: "150", date: true },
   { title: t("app.remaining"), key: "balance", align: "end" },
   { key: "actions" }
]);

// services
const { data, isLoading } = useGetSaleAll();
useGetSaleById({
   id: expandedId,
   enabled: enabled,
   onSuccess: async (item) => {
      expandedItems.value[item.id] = {
         data: item,
         isLoading: false
      };
   }
});

// handlers
const expandHandler = (item: ISale) => {
   if (expandedItems.value[item.id]) {
      return;
   }
   expandedItems.value[item.id] = {
      isLoading: true
   };
   expandedId.value = item.id;
};

const recordHandler = () => {
   dialog.value?.open();
};
</script>
