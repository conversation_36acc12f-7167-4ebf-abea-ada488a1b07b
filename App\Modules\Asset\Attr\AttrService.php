<?php

declare(strict_types=1);

namespace App\Modules\Asset\Attr;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Attr\AttrRequest;
use App\Modules\Asset\Attr\AttrRepository;

class AttrService extends BaseService {
   /** @var AttrRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      AttrRepository $repository
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createAttr(AttrRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required'
         ]);

         $id = $this->create([
            'sort_order' => $request->sort_order
         ]);

         $this->translate($request->translate, [
            'attr_id' => $id
         ], 'asset_attr_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateAttr(AttrRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required'
         ]);

         $this->update($request, [
            'sort_order' => $request->sort_order
         ], [
            'id' => $request->id
         ]);

         $this->translate($request->translate, [
            'attr_id' => $request->id
         ], 'asset_attr_translate');

         return $this->getOne($request->id, $lang_id);
      });
   }
}
