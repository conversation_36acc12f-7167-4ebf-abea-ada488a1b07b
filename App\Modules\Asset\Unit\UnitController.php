<?php

declare(strict_types=1);

namespace App\Modules\Asset\Unit;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Unit\UnitService;
use App\Modules\Asset\Unit\UnitRequest;
use App\Modules\Asset\Unit\UnitResponse;

/**
 * @OA\Tag(name="Unit", description="Birim işlemleri")
 */
class UnitController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected UnitService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Unit"}, path="/unit/", summary="Birim listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllUnit() {
      $this->response(function () {
         $result = $this->service->getAll();

         return array_map(function ($item) {
            $response = new UnitResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Unit"}, path="/unit/{id}", summary="Birim detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getUnit(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id);

         $response = new UnitResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Post(tags={"Unit"}, path="/unit/", summary="Birim ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"title", "type", "intl_id"},
    *       @OA\Property(property="title", type="string", example="Kilogram"),
    *       @OA\Property(property="type", type="string", example="weight"),
    *       @OA\Property(property="intl_id", type="string", example="kg")
    *    ))
    * )
    */
   public function createUnit() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new UnitRequest();
         $request->fromArray($json);
         $result = $this->service->createUnit($request);

         $response = new UnitResponse();
         $response->fromArray($result);

         return $response;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Unit"}, path="/unit/", summary="Birim güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "title", "type", "intl_id"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="title", type="string", example="Kilogram"),
    *       @OA\Property(property="type", type="string", example="weight"),
    *       @OA\Property(property="intl_id", type="string", example="kg")
    *    ))
    * )
    */
   public function updateUnit() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new UnitRequest();
         $request->fromArray($json);
         $result = $this->service->updateUnit($request);

         $response = new UnitResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Unit"}, path="/unit/{id}", summary="Birim sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteUnit(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'id' => $id,
            'deleted_at' => ['IS NULL']
         ]);

         return $result;
      });
   }
}