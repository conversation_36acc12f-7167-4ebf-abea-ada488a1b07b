<template>
   <v-card elevation="2" class="upcoming-payments-widget">
      <v-card-title class="d-flex align-center justify-space-between">
         <div>
            <h3 class="text-h6 font-weight-bold">{{ t("app.upcomingPayments") }}</h3>
            <p class="text-caption text-medium-emphasis ma-0">{{ t("app.next7Days") }}</p>
         </div>
         <v-btn
            v-bind:to="{ name: 'installmentList' }"
            variant="text"
            size="small"
            color="primary">
            {{ t("app.viewAll") }}
            <v-icon end icon="$chevronRight" size="16" />
         </v-btn>
      </v-card-title>

      <v-card-text class="pa-0">
         <div v-if="loading" class="d-flex justify-center align-center pa-6" style="height: 200px;">
            <v-progress-circular indeterminate color="primary" />
         </div>

         <div v-else-if="installments.length === 0" class="d-flex justify-center align-center pa-6" style="height: 200px;">
            <div class="text-center">
               <v-icon icon="$calendarCheck" size="48" color="grey-lighten-1" class="mb-2" />
               <p class="text-body-2 text-medium-emphasis">{{ t("app.noUpcomingPayments") }}</p>
            </div>
         </div>

         <v-list v-else class="pa-0">
            <template v-for="(installment, index) in installments" :key="installment.id">
               <v-list-item
                  v-bind:to="{ name: 'installmentDetail', params: { id: installment.id } }"
                  class="px-4 py-3"
                  :class="{ 'border-b': index < installments.length - 1 }">
                  <template #prepend>
                     <v-avatar
                        size="40"
                        :color="getUrgencyColor(installment)"
                        variant="tonal">
                        <v-icon :icon="getUrgencyIcon(installment)" size="20" />
                     </v-avatar>
                  </template>

                  <v-list-item-title class="font-weight-medium">
                     {{ installment.sale?.customer || installment.sale?.customer?.name || t("app.unknownCustomer") }}
                  </v-list-item-title>

                  <v-list-item-subtitle class="d-flex align-center mt-1">
                     <span class="me-2">{{ formatDate(installment.due_at) }}</span>
                     <v-chip
                        :color="getUrgencyColor(installment)"
                        size="x-small"
                        variant="flat">
                        {{ getUrgencyText(installment) }}
                     </v-chip>
                  </v-list-item-subtitle>

                  <template #append>
                     <div class="text-right">
                        <div class="text-h6 font-weight-bold">
                           {{ formatCurrency(getRemainingAmount(installment)) }}
                        </div>
                        <div class="text-caption text-medium-emphasis">
                           {{ t("app.installment") }} {{ installment.amount || 1 }}
                        </div>
                     </div>
                  </template>
               </v-list-item>
            </template>
         </v-list>
      </v-card-text>
   </v-card>
</template>

<script lang="ts" setup>
import type { IInstallment } from '@/services/InstallmentService';

interface Props {
   installments: IInstallment[];
   loading?: boolean;
}

withDefaults(defineProps<Props>(), {
   loading: false
});

const { t } = useI18n();

// Helper functions
function formatCurrency(value: string | number) {
   const num = typeof value === 'string' ? parseFloat(value) : value;
   return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
   }).format(num || 0);
}

function formatDate(dateString?: string) {
   if (!dateString) return '';
   return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
   });
}

function getRemainingAmount(installment: IInstallment) {
   const amount = parseFloat(installment.amount || '0');
   const payment = parseFloat(installment.payment || '0');
   return amount - payment;
}

function getDaysUntilDue(installment: IInstallment) {
   if (!installment.due_at) return 0;
   const today = new Date();
   const dueDate = new Date(installment.due_at);
   const diffTime = dueDate.getTime() - today.getTime();
   return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function getUrgencyColor(installment: IInstallment) {
   const days = getDaysUntilDue(installment);

   if (days < 0) return 'error';      // Overdue
   if (days === 0) return 'error';    // Due today
   if (days <= 2) return 'warning';   // Due in 1-2 days
   return 'info';                     // Due in 3+ days
}

function getUrgencyIcon(installment: IInstallment) {
   const days = getDaysUntilDue(installment);

   if (days < 0) return '$alertTriangle';  // Overdue
   if (days === 0) return '$clock';         // Due today
   if (days <= 2) return '$alertCircle';   // Due soon
   return '$calendar';                      // Due later
}

function getUrgencyText(installment: IInstallment) {
   const days = getDaysUntilDue(installment);

   if (days < 0) {
      const overdueDays = Math.abs(days);
      return t('app.overdueDays', { days: overdueDays });
   }
   if (days === 0) return t('app.dueToday');
   if (days === 1) return t('app.dueTomorrow');
   return t('app.dueInDays', { days });
}
</script>

<style scoped>
.upcoming-payments-widget {
   height: 100%;
}

.border-b {
   border-bottom: 1px solid rgba(var(--v-theme-outline), 0.12);
}

.v-list-item {
   transition: background-color 0.2s ease;
}

.v-list-item:hover {
   background-color: rgba(var(--v-theme-primary), 0.04);
}
</style>