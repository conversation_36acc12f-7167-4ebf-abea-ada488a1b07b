<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading && isFirst">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               color="primary"
               prepend-icon="$reset"
               @click="resetHandler">
               {{ t("app.reset") }}
            </ActionButton>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <template v-slot:items>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="manufacturer.code"
                     disabled />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="manufacturer.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="manufacturer.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="manufacturer.content"
                     class="max-grow-80"
                     auto-grow
                     disabled
                     no-resize />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="manufacturer.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ manufacturer.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList v-bind:items="[manufacturer.image_path]" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:title>SEO</template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.url") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="manufacturer.url"
                     v-bind:rules="[appRules.required()]">
                     <template v-slot:append-inner>
                        <URLButton
                           v-model="manufacturer.url"
                           v-bind:source="manufacturer.title" />
                        <TranslateButton
                           v-if="language !== 1"
                           v-model="manufacturer.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.metaTitle") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="manufacturer.meta_title" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.metaDescription") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="manufacturer.meta_description"
                     v-bind:rules="[appRules.required()]"
                     class="max-grow-80"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="manufacturer.meta_description" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.metaKeywords") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="manufacturer.meta_keywords" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import URLButton from "@/components/Button/URLButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useDeleteManufacturer, useGetManufacturerById, useOverrideManufacturer } from "../services/ManufacturerService";
import { IManufacturer, IManufacturerStore } from "../utils/types";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const snackbarStore = useSnackbarStore();

// states
const manufacturer = ref({
   is_active: 1
} as IManufacturer);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);
const language = ref(1);

// services
const getManufacturerById = useGetManufacturerById({
   enabled: isEdit,
   params: {
      id: routeId,
      language: language
   },
   onSuccess: (data) => {
      manufacturer.value = { ...data };
   }
});
const overrideManufacturer = useOverrideManufacturer();
const deleteManufacturer = useDeleteManufacturer();

// status
const isLoading = computed(() => getManufacturerById.isLoading.value);
const isFirst = computed(() => getManufacturerById.isFirst.value);
const isPending = computed(() => overrideManufacturer.isPending.value);
const isError = computed(() => getManufacturerById.isError.value);

// handlers
const resetHandler = async () => {
   try {
      await deleteManufacturer.mutateAsync({ id: manufacturer.value.id });
      snackbarStore.add({ text: t("app.recordReset") });
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};

const formHandler = async () => {
   const payload: IManufacturerStore = {
      manufacturer_id: manufacturer.value.id,
      translate: [
         {
            language_id: language.value,
            title: manufacturer.value.title,
            url: manufacturer.value.url,
            meta_title: manufacturer.value.meta_title,
            meta_description: manufacturer.value.meta_description,
            meta_keywords: manufacturer.value.meta_keywords
         }
      ],
      is_active: manufacturer.value.is_active
   };

   try {
      await overrideManufacturer.mutateAsync(payload);
      snackbarStore.add({ text: t("app.recordUpdated") });
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
