import { IAttr, IAttrStore } from "../utils/types";

export const useGetAttrAll = <T extends IAttr>(payload?: TQuery<T[]>) => {
   const options: UseQueryOptions<T[]> = {
      queryKey: ["attr", "attrAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/attr/")).data;
      },
      enabled: payload?.enabled
   };

   return queryWrapper(options, payload);
};

export const useGetAttrById = <T extends IAttr>(payload?: TQuery<T>) => {
   const options: UseQueryOptions<T> = {
      queryKey: ["attr", "attrById", payload?.params?.id, payload?.params?.language],
      queryFn: async ({ signal }) => {
         return (await appAxios.get(`/asset/attr/${toValue(payload?.params?.id)}`, { signal, params: { lang_id: toValue(payload?.params?.language) } })).data;
      },
      enabled: payload?.enabled
   };

   return queryWrapper(options, payload);
};

export const useUpdateAttr = <T extends IAttr>() => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["attr", "updateAttr"],
      mutationFn: async (data: IAttrStore): Promise<TResponse<T>> => {
         return (await appAxios.put("/asset/attr/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["attr"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateAttr = <T extends IAttr>() => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["attr", "createAttr"],
      mutationFn: async (data: IAttrStore): Promise<TResponse<T>> => {
         return (await appAxios.post("/asset/attr/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["attr"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
