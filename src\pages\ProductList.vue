<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
            <RecordButton
               v-bind:disabled="isLoading"
               color="primary"
               @click="recordHandler">
               {{ t("app.add") }}
            </RecordButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="flex-center text-base">{{ t("app.productList") }}</v-card-title>
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:hover="false"
            v-bind:items="data">
            <template v-slot:item.is_active="{ item }">
               <v-chip v-bind:color="item.is_active ? 'success' : undefined">
                  {{ item.is_active ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>

            <template v-slot:item.category="{ item }">
               {{ item.category?.title || "-" }}
            </template>

            <template v-slot:item.actions="{ item }">
               <ActionButton
                  icon="$trash"
                  @click="removeHandler(item)" />
               <ActionButton
                  icon="$edit"
                  @click="recordHandler(item)" />
            </template>
         </DataTable>
      </Card>

      <ProductDialog ref="dialog" />
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import RecordButton from "@/components/Button/RecordButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { IProduct, useDeleteProduct, useGetProductAll } from "../services/ProductService";
const ProductDialog = defineAsyncComponent(() => import("@/components/Dialog/ProductDialog.vue"));

// hooks
const { t } = useI18n();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const dialog = ref<InstanceType<typeof ProductDialog>>();
const filter = ref();
const selected = ref([]);
const headers = computed((): THeader<IProduct>[] => [
   { title: t("app.code"), key: "code", width: "150" },
   { title: t("app.title"), key: "title" },
   { title: t("app.price"), key: "price", width: "150", money: "₺" },
   { title: t("app.stock"), key: "stock", width: "150" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("app.category"), key: "category", width: "250" },
   { key: "actions", width: "60" }
]);

// services
const { data, isLoading } = useGetProductAll();
const deleteProduct = useDeleteProduct();

// handlers
const recordHandler = (item?: IProduct) => {
   dialog.value?.open(item);
};

const removeHandler = async (item: IProduct) => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteRecord")
      });

      if (confirm) {
         await deleteProduct.mutateAsync(item.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>
