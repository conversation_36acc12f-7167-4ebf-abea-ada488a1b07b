<?php

declare(strict_types=1);

use System\Migration\Migration;

class web_product_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `web_product_translate` (
         `product_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `title` VARCHAR(250) NOT NULL,
         `url` VARCHAR(250) NULL DEFAULT NULL,
         `meta_title` VARCHAR(250) NULL DEFAULT NULL,
         `meta_description` VARCHAR(250) NULL DEFAULT NULL,
         `meta_keywords` VARCHAR(250) NULL DEFAULT NULL,
         PRIMARY KEY (`product_id`, `language_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `web_product_translate`");
   }
}
