<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_standard_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_standard_translate` (
         `standard_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `title` VARCHAR(250) NOT NULL,
         `content` TEXT NULL DEFAULT NULL,
         PRIMARY KEY (`standard_id`, `language_id`)
      )");
      // FOREIGN KEY (`standard_id`) REFERENCES `standard`(`id`),
      // FOREIGN KEY (`language_id`) REFERENCES `language`(`id`)

      $this->database->table('asset_standard_translate')->insert([
         'standard_id' => 1,
         'language_id' => 1,
         'title' => 'EN388',
         'content' => 'EN388 standartları, koruyucu eldivenler için minimum performans standartlarını belirler.',
      ])->prepare()->execute();

      $this->database->table('asset_standard_translate')->insert([
         'standard_id' => 1,
         'language_id' => 2,
         'title' => 'EN388',
         'content' => 'EN388 standards specify the minimum performance requirements for protective gloves.',
      ])->prepare()->execute();

      $this->database->table('asset_standard_translate')->insert([
         'standard_id' => 2,
         'language_id' => 1,
         'title' => 'EN420',
         'content' => 'EN420 standartları, koruyucu eldivenler için minimum performans standartlarını belirler.',
      ])->prepare()->execute();

      $this->database->table('asset_standard_translate')->insert([
         'standard_id' => 2,
         'language_id' => 2,
         'title' => 'EN420',
         'content' => 'EN420 standards specify the minimum performance requirements for protective gloves.',
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_standard_translate`");
   }
}
