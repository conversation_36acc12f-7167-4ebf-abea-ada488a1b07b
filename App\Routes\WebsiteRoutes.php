<?php

declare(strict_types=1);

use App\Core\Middlewares\Auth;
use App\Modules\Website\Product\ProductController;
use App\Modules\Website\Category\CategoryController;
use App\Modules\Website\Manufacturer\ManufacturerController;

/** @var System\Router\Router $router */

// Category routes
$router->prefix('v1/website/category')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CategoryController::class, 'getAllCategory']);
   $router->put('/', [CategoryController::class, 'overrideCategory']);
   $router->delete('/{id}', [CategoryController::class, 'deleteCategory'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CategoryController::class, 'getCategory'])->where(['id' => '([0-9]+)']);
});

// Manufacturer routes
$router->prefix('v1/website/manufacturer')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ManufacturerController::class, 'getAllManufacturer']);
   $router->put('/', [ManufacturerController::class, 'overrideManufacturer']);
   $router->delete('/{id}', [ManufacturerController::class, 'deleteManufacturer'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ManufacturerController::class, 'getManufacturer'])->where(['id' => '([0-9]+)']);
});

// Product routes
$router->prefix('v1/website/product')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ProductController::class, 'getAllProduct']);
   $router->put('/', [ProductController::class, 'overrideProduct']);
   $router->delete('/{id}', [ProductController::class, 'deleteProduct'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
});
