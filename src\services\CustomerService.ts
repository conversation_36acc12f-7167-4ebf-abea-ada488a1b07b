export interface ICustomer extends IDefaultFields {
   id: number;
   name: string;
   surname: string;
   email?: string;
   phone: string;
   tckn?: string;
   address?: string;
   balance: number;
   debit: string;
   is_risky: number;
   is_active: number;
   notes?: string;
}

export interface ICustomerStore {
   id?: number;
   name: string;
   surname: string;
   email?: string;
   phone: string;
   tckn?: string;
   address?: string;
   balance?: number;
   is_risky?: number;
   is_active?: number;
   notes?: string;
}

export const useGetCustomerAll = (payload?: TQuery<ICustomer[]>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerAll"],
      queryFn: async () => {
         return (await appAxios.get("/customer/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCustomerById = (payload?: { id?: MaybeRef<string | number> } & TQuery<ICustomer>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerById", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/customer/${toValue(payload?.id)}`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateCustomer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["customer", "updateCustomer"],
      mutationFn: async (data: ICustomerStore): Promise<TResponse<ICustomer>> => {
         return (await appAxios.put("/customer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["customer"] });
      }
   });
};

export const useCreateCustomer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["customer", "createCustomer"],
      mutationFn: async (data: ICustomerStore): Promise<TResponse<ICustomer>> => {
         return (await appAxios.post("/customer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["customer"] });
      }
   });
};

export const useDeleteCustomer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["customer", "deleteCustomer"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/customer/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["customer"] });
      }
   });
};

export const useGetCustomerSales = (payload?: { id?: MaybeRef<string | number> } & TQuery<any>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerSales", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/customer/${toValue(payload?.id)}/sales`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCustomerInstallments = (payload?: { id?: MaybeRef<string | number> } & TQuery<any>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerInstallments", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/customer/${toValue(payload?.id)}/installments`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};
