import { IProduct, IProductStore } from "../utils/types";

export const useGetProductAll = (payload?: TQuery<IProduct[]>) => {
   const options = computed(() => ({
      queryKey: ["product", "productAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/product/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetProductById = (payload?: TQuery<IProduct>) => {
   const options = computed(() => ({
      queryKey: ["product", "productById", payload?.params?.id, payload?.params?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/product/${toValue(payload?.params?.id)}`, { signal, params: { lang_id: toValue(payload?.params?.language) } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "updateProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.put("/asset/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "createProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.post("/asset/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
