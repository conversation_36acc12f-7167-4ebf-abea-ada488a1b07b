<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_category extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_category` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `code` VARCHAR(50) NOT NULL,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         `parent_id` INT NOT NULL DEFAULT 0,
         `group_id` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('asset_category')->insert([
         'code' => 'CAT001',
         'is_active' => 1,
         'sort_order' => 1,
         'parent_id' => 0,
         'group_id' => 1,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_category`");
   }
}
