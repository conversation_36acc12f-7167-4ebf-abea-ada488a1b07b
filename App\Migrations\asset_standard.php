<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_standard extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_standard` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('asset_standard')->insert([
         'sort_order' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_standard')->insert([
         'sort_order' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_standard`");
   }
}
