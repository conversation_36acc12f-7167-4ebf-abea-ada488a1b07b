<?php

declare(strict_types=1);

namespace App\Modules\Asset\Competing;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Product\ProductRepository;
use App\Modules\Asset\Competing\CompetingRequest;
use App\Modules\Asset\Competing\CompetingRepository;

class CompetingService extends BaseService {
   /** @var CompetingRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      protected ProductRepository $productRepository,
      CompetingRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(): array {
      $result = $this->repository->findAll();

      return array_map(function ($item) {
         $item['category_list'] = $this->repository->findCategory($item['id']);
         $item['manufacturer_list'] = $this->repository->findManufacturer($item['id']);
         $item['attr_list'] = $this->repository->findAttr($item['id']);
         $item['standard_list'] = $this->repository->findStandard($item['id']);
         $item['product_list'] = array_map(function ($product) {
            $product['image_list'] = $this->productRepository->findImage($product['id']);
            return $product;
         }, $this->repository->findProduct($item['id']));
         return $item;
      }, $result);
   }

   // override
   public function getOne(int $id): array {
      $result = $this->repository->findOne($id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $result['category_list'] = $this->repository->findCategory($result['id']);
      $result['manufacturer_list'] = $this->repository->findManufacturer($result['id']);
      $result['attr_list'] = $this->repository->findAttr($result['id']);
      $result['standard_list'] = $this->repository->findStandard($result['id']);
      $result['product_list'] = array_map(function ($product) {
         $product['image_list'] = $this->productRepository->findImage($product['id']);
         return $product;
      }, $this->repository->findProduct($result['id']));

      return $result;
   }

   public function createCompeting(CompetingRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->validate($request->toArray(), [
            'title' => 'required',
            'content' => 'required',
            'price' => 'numeric',
            'currency' => 'nullable',
            'image_path' => 'nullable'
         ]);

         $id = $this->create([
            'title' => $request->title,
            'content' => $request->content,
            'price' => $request->price,
            'currency' => $request->currency,
            'image_path' => $request->image_path
         ]);

         $this->createRelation($request, $id);

         return $this->getOne($id);
      });
   }

   public function updateCompeting(CompetingRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
            'price' => 'numeric',
            'currency' => 'nullable',
            'image_path' => 'nullable'
         ]);

         $this->update($request, [
            'title' => $request->title,
            'content' => $request->content,
            'price' => $request->price,
            'currency' => $request->currency,
            'image_path' => $request->image_path
         ], [
            'id' => $request->id
         ]);

         $this->updateRelation($request, [
            'competing_category',
            'competing_manufacturer',
            'competing_attr',
            'competing_standard',
            'competing_product'
         ]);

         return $this->getOne($request->id);
      });
   }

   private function createRelation(CompetingRequest $request, int $id): void {
      if (isset($request->competing_product) && is_array($request->competing_product)) {
         foreach ($request->competing_product as $product_id) {
            $product = $this->repository->create([
               'competing_id' => $id,
               'product_id' => $product_id
            ], 'asset_competing_product');

            if ($product->affectedRows() <= 0) {
               throw new SystemException('Product relation not created', 400);
            }
         }
      }

      if (isset($request->competing_category) && is_array($request->competing_category)) {
         foreach ($request->competing_category as $category_id) {
            $category = $this->repository->create([
               'competing_id' => $id,
               'category_id' => $category_id
            ], 'asset_competing_category');

            if ($category->affectedRows() <= 0) {
               throw new SystemException('Category relation not created', 400);
            }
         }
      }

      if (isset($request->competing_manufacturer) && is_array($request->competing_manufacturer)) {
         foreach ($request->competing_manufacturer as $manufacturer_id) {
            $manufacturer = $this->repository->create([
               'competing_id' => $id,
               'manufacturer_id' => $manufacturer_id
            ], 'asset_competing_manufacturer');

            if ($manufacturer->affectedRows() <= 0) {
               throw new SystemException('Manufacturer relation not created', 400);
            }
         }
      }

      if (isset($request->competing_attr) && is_array($request->competing_attr)) {
         foreach ($request->competing_attr as $attr_id) {
            $attr = $this->repository->create([
               'competing_id' => $id,
               'attr_id' => $attr_id
            ], 'asset_competing_attr');

            if ($attr->affectedRows() <= 0) {
               throw new SystemException('Attribute relation not created', 400);
            }
         }
      }

      if (isset($request->competing_standard) && is_array($request->competing_standard)) {
         foreach ($request->competing_standard as $standard) {
            $standard_id = is_array($standard) ? $standard['standard_id'] : $standard;
            $value = is_array($standard) ? ($standard['value'] ?? null) : null;

            $standard = $this->repository->create([
               'competing_id' => $id,
               'standard_id' => $standard_id,
               'value' => $value
            ], 'asset_competing_standard');

            if ($standard->affectedRows() <= 0) {
               throw new SystemException('Standard relation not created', 400);
            }
         }
      }
   }

   private function updateRelation(CompetingRequest $request, array $tables): void {
      foreach ($tables as $table) {
         if (isset($request->$table) && is_array($request->$table)) {
            $this->repository->hardDelete([
               'competing_id' => $request->id
            ], 'asset_' . $table);
         }
      }

      $this->createRelation($request, $request->id);
   }

   public function delete(array $where, ?string $table = null): bool {
      return $this->transaction(function () use ($where, $table) {
         $this->repository->hardDelete([
            'id' => $where['competing_id']
         ], $table);
         $this->repository->hardDelete($where, 'asset_competing_attr');
         $this->repository->hardDelete($where, 'asset_competing_category');
         $this->repository->hardDelete($where, 'asset_competing_manufacturer');
         $this->repository->hardDelete($where, 'asset_competing_standard');
         $this->repository->hardDelete($where, 'asset_competing_product');

         return true;
      });
   }
}
