<?php

declare(strict_types=1);

namespace App\Modules\Asset\Manufacturer;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Manufacturer\ManufacturerRequest;
use App\Modules\Asset\Manufacturer\ManufacturerRepository;

class ManufacturerService extends BaseService {
   /** @var ManufacturerRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ManufacturerRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1, ?array $request = null): array {
      $competing = $request['competing'] ?? null;

      if ($competing === '1' || $competing === '0') {
         $result = $this->repository->findCompeting($lang_id, (int) $competing);
      } else {
         $result = $this->repository->findAll($lang_id);
      }

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createManufacturer(ManufacturerRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'code' => 'required',
            'title' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'is_competing' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric'
         ]);

         $id = $this->create([
            'code' => $request->code,
            'title' => $request->title,
            'image_path' => $request->image_path,
            'is_active' => $request->is_active,
            'is_competing' => $request->is_competing,
            'sort_order' => $request->sort_order,
         ]);

         $this->translate($request->translate, [
            'manufacturer_id' => $id
         ], 'asset_manufacturer_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateManufacturer(ManufacturerRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'code' => 'required',
            'title' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'is_competing' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric'
         ]);

         $this->update($request, [
            'code' => $request->code,
            'title' => $request->title,
            'image_path' => $request->image_path,
            'is_active' => $request->is_active,
            'is_competing' => $request->is_competing,
            'sort_order' => $request->sort_order,
         ], [
            'id' => $request->id
         ]);

         $this->translate($request->translate, [
            'manufacturer_id' => $request->id,
         ], 'asset_manufacturer_translate');

         return $this->getOne($request->id, $lang_id);
      });
   }

   public function mikroManufacturer(ManufacturerRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'code' => 'required',
            'title' => 'required',
         ]);

         $id = $this->create([
            'code' => $request->code,
            'title' => $request->title,
            'image_path' => $request->image_path,
            'is_active' => $request->is_active,
            'is_competing' => $request->is_competing,
            'sort_order' => $request->sort_order,
         ]);

         return $this->getOne($id, $lang_id);
      });
   }
}
