<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
         </template>

         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading"
               v-bind:to="{ name: 'asset.attrCreate' }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("module.attributeList") }}
         </template>

         <!-- <template v-slot:items>
            <v-tabs>
               <v-tab
                  v-bind:value="1">
                  {{ t("module.attribute", 2) }}
               </v-tab>
            </v-tabs>
         </template> -->

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            @row:click="(item) => $router.push({ name: 'asset.attrDetail', params: { id: item.id } })" />
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetAttrAll } from "../services/AttrService";
import { IAttr } from "../utils/types";

// hooks
const { t } = useI18n();

// states
const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IAttr>[] => [
   { title: t("app.title"), key: "title" },
   { title: t("app.createDate"), key: "created_at", width: "250", date: 'fullDate' },
   { title: t("app.updateDate"), key: "updated_at", width: "250", date: 'fullDate' }
]);

// services
const { data, isLoading } = useGetAttrAll();
</script>
