export const registerMenu = async (): Promise<void> => {
   const appStore = useAppStore();
   appStore.setMenuLoading(true);
   await loadMenu()
      .then((menu) => {
         appStore.setMenu(menu);
      })
      .finally(() => {
         appStore.setMenuLoading(false);
      });
};

export const appMenu: TList[] = [
   {
      itemType: "subheader",
      itemTitle: "Setre Giyim"
   },
   {
      itemTitle: i18n.global.t("app.dashboard"),
      itemProps: {
         prependIcon: "$dashboard",
         to: "/dashboard"
      }
   },
   {
      itemTitle: i18n.global.t("app.product", 2),
      itemProps: {
         prependIcon: "$product",
         to: "/product"
      }
   },
   {
      itemTitle: i18n.global.t("app.category", 2),
      itemProps: {
         prependIcon: "$category",
         to: "/category"
      }
   },
   {
      itemTitle: i18n.global.t("app.customer", 2),
      itemProps: {
         prependIcon: "$customer",
         to: "/customer"
      }
   },
   {
      itemTitle: i18n.global.t("app.sale", 2),
      itemProps: {
         prependIcon: "$sale",
         to: "/sale"
      }
   }
];
