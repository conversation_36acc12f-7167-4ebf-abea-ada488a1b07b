import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const websiteRoutes: RouteRecordRaw[] = [
   {
      path: "/website",
      meta: {
         layout: DefaultLayout,
         module: "website"
      },
      children: [
         {
            path: "product",
            meta: {
               title: i18n.global.t("module.product"),
               breadcrumb: i18n.global.t("module.productList")
            },
            children: [
               {
                  path: "",
                  name: "website.productList",
                  component: getComponent(() => import("../pages/ProductList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "website.productDetail",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               }
            ]
         },
         {
            path: "category",
            meta: {
               title: i18n.global.t("module.category"),
               breadcrumb: i18n.global.t("module.categoryList")
            },
            children: [
               {
                  path: "",
                  name: "website.categoryList",
                  component: getComponent(() => import("../pages/CategoryList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "website.categoryDetail",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               }
            ]
         },
         {
            path: "manufacturer",
            meta: {
               title: i18n.global.t("module.manufacturer"),
               breadcrumb: i18n.global.t("module.manufacturerList")
            },
            children: [
               {
                  path: "",
                  name: "website.manufacturerList",
                  component: getComponent(() => import("../pages/ManufacturerList.vue"))
               },
               {
                  path: ":id([0-9]+)",
                  name: "website.manufacturerDetail",
                  component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
                  meta: {
                     breadcrumb: i18n.global.t("app.detailRecord")
                  }
               }
            ]
         }
      ]
   }
];
