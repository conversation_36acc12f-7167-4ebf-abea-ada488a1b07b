import { ITaxrate, ITaxrateStore } from "../utils/types";

export const useGetTaxrateAll = (payload?: TQuery<ITaxrate[]>) => {
   const options = computed(() => ({
      queryKey: ["taxrate", "taxrateAll"],
      queryFn: async () => {
         return (await appAxios.get("/asset/taxrate/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetTaxrateById = (payload?: TQuery<ITaxrate>) => {
   const options = computed(() => ({
      queryKey: ["taxrate", "taxrateById", payload?.params?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/asset/taxrate/${toValue(payload?.params?.id)}`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateTaxrate = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["taxrate", "updateTaxrate"],
      mutationFn: async (data: ITaxrateStore): Promise<TResponse<ITaxrate>> => {
         return (await appAxios.put("/asset/taxrate/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["taxrate"] });
      }
   });
};

export const useCreateTaxrate = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["taxrate", "createTaxrate"],
      mutationFn: async (data: ITaxrateStore): Promise<TResponse<ITaxrate>> => {
         return (await appAxios.post("/asset/taxrate/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["taxrate"] });
      }
   });
};
