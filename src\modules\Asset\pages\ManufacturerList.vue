<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
         </template>

         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading"
               v-bind:to="{ name: 'asset.manufacturerCreate' }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("module.manufacturerList") }}
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            @row:click="(item) => $router.push({ name: 'asset.manufacturerDetail', params: { id: item.id } })">
            <template v-slot:item.is_active="{ item }">
               <v-chip v-bind:color="item.is_active ? 'success' : undefined">
                  {{ item.is_active ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>

            <template v-slot:item.is_competing="{ item }">
               <v-chip v-bind:color="item.is_competing ? 'primary' : undefined">
                  {{ item.is_competing ? t("app.yes") : t("app.no") }}
               </v-chip>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetManufacturerAll } from "../services/ManufacturerService";
import { IManufacturer } from "../utils/types";

// hooks
const { t } = useI18n();

// states
const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IManufacturer>[] => [
   { title: t("app.code"), key: "code", width: "100" },
   { title: t("app.title"), key: "title" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("module.competingBrand"), key: "is_competing", width: "150" },
   { title: t("app.createDate"), key: "created_at", width: "250", date: true },
   { title: t("app.updateDate"), key: "updated_at", width: "250", date: true }
]);

// services
const { data, isLoading } = useGetManufacturerAll();
</script>
