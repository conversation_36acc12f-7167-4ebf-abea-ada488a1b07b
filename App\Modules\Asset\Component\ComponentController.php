<?php

declare(strict_types=1);

namespace App\Modules\Asset\Component;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Component\ComponentService;
use App\Modules\Asset\Component\ComponentRequest;
use App\Modules\Asset\Component\ComponentResponse;

/**
 * @OA\Tag(name="Component", description="Bileşen işlemleri")
 */
class ComponentController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ComponentService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Component"}, path="/component/", summary="Bileşen listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllComponent() {
      $this->response(function () {
         $result = $this->service->getAll();

         return array_map(function ($item) {
            $response = new ComponentResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Component"}, path="/component/{id}", summary="Bileşen detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getComponent(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id);

         $response = new ComponentResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Post(tags={"Component"}, path="/component/", summary="Bileşen ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"title", "quantity", "min_quantity", "manufacturer_id", "location_id", "supplier_id"},
    *       @OA\Property(property="title", type="string", example="Bileşen Başlığı"),
    *       @OA\Property(property="note", type="string", example="Bileşen Notu"),
    *       @OA\Property(property="model", type="string", example="Bileşen Modeli"),
    *       @OA\Property(property="quantity", type="number", example=10),
    *       @OA\Property(property="min_quantity", type="number", example=5),
    *       @OA\Property(property="serial", type="string", example="Bileşen Seri Numarası"),
    *       @OA\Property(property="manufacturer_id", type="number", example=1),
    *       @OA\Property(property="location_id", type="number", example=1),
    *       @OA\Property(property="supplier_id", type="number", example=1),
    *       @OA\Property(property="order_serial", type="string", example="Sipariş Seri Numarası"),
    *       @OA\Property(property="order_number", type="string", example="Sipariş Numarası"),
    *       @OA\Property(property="purchase_date", type="string", example="2021-01-01"),
    *       @OA\Property(property="purchase_price", type="number", example=100.00),
    *       @OA\Property(property="purchase_currency", type="string", example="TRY"),
    *       @OA\Property(property="warranty_duration", type="number", example=1),
    *       @OA\Property(property="warranty_type", type="string", example="year")
    *    ))
    * )
    */
   public function createComponent() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new ComponentRequest();
         $request->fromArray($json);
         $result = $this->service->createComponent($request);

         $response = new ComponentResponse();
         $response->fromArray($result);

         return $response;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Component"}, path="/component/", summary="Bileşen güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "title", "quantity", "min_quantity", "manufacturer_id", "location_id", "supplier_id"},
    *       @OA\Property(property="id", type="number", example=1),
    *       @OA\Property(property="title", type="string", example="Bileşen Başlığı"),
    *       @OA\Property(property="note", type="string", example="Bileşen Notu"),
    *       @OA\Property(property="model", type="string", example="Bileşen Modeli"),
    *       @OA\Property(property="quantity", type="number", example=10),
    *       @OA\Property(property="min_quantity", type="number", example=5),
    *       @OA\Property(property="serial", type="string", example="Bileşen Seri Numarası"),
    *       @OA\Property(property="manufacturer_id", type="number", example=1),
    *       @OA\Property(property="location_id", type="number", example=1),
    *       @OA\Property(property="supplier_id", type="number", example=1),
    *       @OA\Property(property="order_serial", type="string", example="Sipariş Seri Numarası"),
    *       @OA\Property(property="order_number", type="string", example="Sipariş Numarası"),
    *       @OA\Property(property="purchase_date", type="string", example="2021-01-01"),
    *       @OA\Property(property="purchase_price", type="number", example=100.00),
    *       @OA\Property(property="purchase_currency", type="string", example="TRY"),
    *       @OA\Property(property="warranty_duration", type="number", example=1),
    *       @OA\Property(property="warranty_type", type="string", example="year")
    *    ))
    * )
    */
   public function updateComponent() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new ComponentRequest();
         $request->fromArray($json);
         $result = $this->service->updateComponent($request);

         $response = new ComponentResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Component"}, path="/component/{id}", summary="Bileşen sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteComponent(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'id' => $id,
            'deleted_at' => ['IS NULL']
         ]);

         return $result;
      });
   }
}
