<?php

declare(strict_types=1);

namespace App\Modules\Asset\Component;

use App\Core\Abstracts\BaseResource;

class ComponentResponse extends BaseResource {
   public int $id;
   public string $title;
   public ?string $note;
   public ?string $model;
   public int $quantity;
   public int $min_quantity;
   public ?string $serial;
   public int $manufacturer;
   public int $location;
   public int $supplier;
   public ?string $order_serial;
   public ?string $order_number;
   public ?string $purchase_date;
   public ?string $purchase_price;
   public ?string $purchase_currency;
   public ?int $warranty_duration;
   public ?string $warranty_type; // day, month, year
   public array $image_list;
}
