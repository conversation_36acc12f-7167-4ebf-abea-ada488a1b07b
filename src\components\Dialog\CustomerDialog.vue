<template>
   <RecordDialog
      ref="recordDialog"
      @after-leave="reset">
      <v-row no-gutters>
         <v-col md="4">
            <v-list-subheader>{{ t("app.name") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field
               v-model="customer.name"
               v-bind:rules="[appRules.required()]"
               v-ucwords />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.surname") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field
               v-model="customer.surname"
               v-bind:rules="[appRules.required()]"
               v-ucwords />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.phone") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field
               v-model="customer.phone"
               v-bind:rules="[appRules.required()]"
               v-maska="{ mask: '0A## ### ####', tokens: { A: { pattern: /[1-9]/ } } }" />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.email") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field
               validate-on="blur"
               v-model="customer.email"
               v-bind:rules="[appRules.email()]" />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.tckn") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-text-field v-model="customer.tckn" />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.address") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-textarea
               v-model="customer.address"
               auto-grow
               no-resize />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.notes") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-textarea
               v-model="customer.notes"
               auto-grow
               no-resize />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.balance") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <NumberInput v-model="customer.balance" />
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.riskStatus") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-switch
               v-model="customer.is_risky"
               v-bind:false-value="0"
               v-bind:ripple="false"
               v-bind:true-value="1"
               color="error"
               density="compact">
               <template v-slot:label>
                  <div class="text-sm">{{ customer.is_risky ? t("app.yes") : t("app.no") }}</div>
               </template>
            </v-switch>
         </v-col>

         <v-col md="4">
            <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
         </v-col>
         <v-col md="8">
            <v-switch
               v-model="customer.is_active"
               v-bind:false-value="0"
               v-bind:ripple="false"
               v-bind:true-value="1"
               color="primary"
               density="compact">
               <template v-slot:label>
                  <div class="text-sm">{{ customer.is_active ? t("app.active") : t("app.passive") }}</div>
               </template>
            </v-switch>
         </v-col>
      </v-row>
   </RecordDialog>
</template>

<script lang="ts" setup>
import type { ICustomer, ICustomerStore } from "@/services/CustomerService";
import { useCreateCustomer, useGetCustomerById, useUpdateCustomer } from "@/services/CustomerService";
import NumberInput from "../Input/NumberInput.vue";
import RecordDialog from "../Layout/Default/Dialog/RecordDialog.vue";

// hooks
const { t } = useI18n();
const snackbarStore = useSnackbarStore();

// initials
const customerInitial = {
   is_active: 1,
   is_risky: 0,
   notes: ""
} as ICustomer;

// states
const recordDialog = ref<InstanceType<typeof RecordDialog>>();
const customer = ref({ ...customerInitial });
const dialogId = computed(() => customer.value.id);
const enabled = computed(() => !!dialogId.value);

// services
const { isLoading } = useGetCustomerById({
   id: dialogId,
   enabled: enabled,
   onSuccess: (item) => {
      customer.value = { ...item };
   }
});
const createCustomer = useCreateCustomer();
const updateCustomer = useUpdateCustomer();

// handlers
const open = async (item?: ICustomer) => {
   try {
      customer.value.id = item?.id || 0;

      const confirm = await recordDialog.value?.open({
         width: 800,
         title: item?.id ? t("app.detailRecord") : t("app.createRecord"),
         loading: isLoading
      });

      if (confirm) {
         const payload: ICustomerStore = {
            ...customer.value,
            balance: customer.value.balance,
            phone: customer.value.phone.replace(/\s+/g, "")
         };

         if (!item?.id) {
            await createCustomer.mutateAsync(payload);
            snackbarStore.add({ text: t("app.recordCreated") });
         } else {
            await updateCustomer.mutateAsync(payload);
            snackbarStore.add({ text: t("app.recordUpdated") });
         }
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   } finally {
      recordDialog.value?.close();
   }
};

const reset = () => {
   customer.value = { ...customerInitial };
};

defineExpose({ open, isLoading });
</script>
