<?php

declare(strict_types=1);

namespace App\Modules\Asset\Product;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Product\ProductRequest;
use App\Modules\Asset\Product\ProductRepository;

class ProductService extends BaseService {
   /** @var ProductRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ProductRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return array_map(function ($item) {
         $item['category_list'] = $this->repository->findCategory($item['id']);
         $item['manufacturer_list'] = $this->repository->findManufacturer($item['id']);
         $item['attr_list'] = $this->repository->findAttr($item['id']);
         $item['standard_list'] = $this->repository->findStandard($item['id']);
         $item['image_list'] = $this->repository->findImage($item['id']);

         return $item;
      }, $result);
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $result['category_list'] = $this->repository->findCategory($result['id']);
      $result['manufacturer_list'] = $this->repository->findManufacturer($result['id']);
      $result['attr_list'] = $this->repository->findAttr($result['id']);
      $result['standard_list'] = $this->repository->findStandard($result['id']);
      $result['image_list'] = $this->repository->findImage($result['id']);

      return $result;
   }

   public function createProduct(ProductRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'product_category' => 'required|must_be_array',
            'product_manufacturer' => 'required|must_be_array',
            'product_attr' => 'required|must_be_array',
            'product_standard' => 'must_be_array',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $id = $this->create([
            'code' => $request->code,
            'is_active' => $request->is_active,
            'sort_order' => $request->sort_order
         ]);

         if (isset($request->image_path) && is_array($request->image_path)) {
            foreach ($request->image_path as $path) {
               $this->create([
                  'product_id' => $id,
                  'image_path' => $path
               ], 'asset_product_image');
            }
         }

         $this->translate($request->translate, [
            'product_id' => $id
         ], 'asset_product_translate');

         $this->createRelation($request, $id);

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateProduct(ProductRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'code' => 'required',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'image_path' => 'nullable',
            'product_category' => 'required|must_be_array',
            'product_manufacturer' => 'required|must_be_array',
            'product_attr' => 'required|must_be_array',
            'product_standard' => 'must_be_array',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $this->update($request, [
            'code' => $request->code,
            'is_active' => $request->is_active,
            'sort_order' => $request->sort_order
         ], [
            'id' => $request->id
         ]);

         if (isset($request->image_path) && is_array($request->image_path)) {
            foreach ($request->image_path as $path) {
               $this->create([
                  'product_id' => $request->id,
                  'image_path' => $path
               ], 'asset_product_image');
            }
         }

         $this->translate($request->translate, [
            'product_id' => $request->id
         ], 'asset_product_translate');

         $this->updateRelation($request, [
            'product_category',
            'product_manufacturer',
            'product_attr',
            'product_standard'
         ]);

         return $this->getOne($request->id, $lang_id);
      });
   }

   private function createRelation(ProductRequest $request, int $id): void {
      if (isset($request->product_category) && is_array($request->product_category)) {
         foreach ($request->product_category as $category_id) {
            $category = $this->repository->create([
               'product_id' => $id,
               'category_id' => $category_id
            ], 'asset_product_category');

            if ($category->affectedRows() <= 0) {
               throw new SystemException('Category relation not created', 400);
            }
         }
      }

      if (isset($request->product_manufacturer) && is_array($request->product_manufacturer)) {
         foreach ($request->product_manufacturer as $manufacturer_id) {
            $manufacturer = $this->repository->create([
               'product_id' => $id,
               'manufacturer_id' => $manufacturer_id
            ], 'asset_product_manufacturer');

            if ($manufacturer->affectedRows() <= 0) {
               throw new SystemException('Manufacturer relation not created', 400);
            }
         }
      }

      if (isset($request->product_attr) && is_array($request->product_attr)) {
         foreach ($request->product_attr as $attr_id) {
            $attr = $this->repository->create([
               'product_id' => $id,
               'attr_id' => $attr_id
            ], 'asset_product_attr');

            if ($attr->affectedRows() <= 0) {
               throw new SystemException('Attr relation not created', 400);
            }
         }
      }

      if (isset($request->product_standard) && is_array($request->product_standard)) {
         foreach ($request->product_standard as $standard) {
            $standard_id = is_array($standard) ? $standard['standard_id'] : $standard;
            $value = is_array($standard) ? ($standard['value'] ?? null) : null;

            $standard = $this->repository->create([
               'product_id' => $id,
               'standard_id' => $standard_id,
               'value' => $value
            ], 'asset_product_standard');

            if ($standard->affectedRows() <= 0) {
               throw new SystemException('Standard relation not created', 400);
            }
         }
      }
   }

   private function updateRelation(ProductRequest $request, array $tables): void {
      foreach ($tables as $table) {
         if (isset($request->$table) && is_array($request->$table)) {
            $this->repository->hardDelete([
               'product_id' => $request->id
            ], 'asset_' . $table);
         }
      }

      $this->createRelation($request, $request->id);
   }
}
