<?php

declare(strict_types=1);

namespace App\Modules\Website\Manufacturer;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Website\Manufacturer\ManufacturerService;
use App\Modules\Website\Manufacturer\ManufacturerRequest;
use App\Modules\Website\Manufacturer\ManufacturerResponse;

/**
 * @OA\Tag(name="Website Manufacturer", description="Website üretici işlemleri")
 */
class ManufacturerController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ManufacturerService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Website Manufacturer"}, path="/manufacturer/", summary="Website üretici listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllManufacturer() {
      $this->response(function () {
         $result = $this->service->getAll($this->language());

         return array_map(function ($item) {
            $response = new ManufacturerResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(
    *    tags={"Website Manufacturer"}, path="/manufacturer/{id}", summary="Website üretici detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getManufacturer(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language());

         $response = new ManufacturerResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Put(
    *    tags={"Website Manufacturer"}, path="/manufacturer/", summary="Website üretici override güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(@OA\JsonContent(
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="WEB-MAN001"),
    *       @OA\Property(property="image_path", type="string", example="/uploads/manufacturer.jpg"),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="object",
    *          @OA\Property(property="language_id", type="integer", example=1),
    *          @OA\Property(property="title", type="string", example="Üretici Başlığı"),
    *          @OA\Property(property="content", type="string", example="Üretici açıklaması"),
    *          @OA\Property(property="url", type="string", example="uretici-basligi"),
    *          @OA\Property(property="meta_title", type="string", example="Meta Başlık"),
    *          @OA\Property(property="meta_description", type="string", example="Meta açıklama"),
    *          @OA\Property(property="meta_keywords", type="string", example="anahtar, kelimeler")
    *       )
    *    ))
    * )
    */
   public function overrideManufacturer() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new ManufacturerRequest();
         $request->fromArray($json);
         $result = $this->service->overrideManufacturer($request, $this->language());

         $response = new ManufacturerResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Website Manufacturer"}, path="/manufacturer/{id}", summary="Website üretici override sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteManufacturer(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'manufacturer_id' => $id
         ]);

         return $result;
      });
   }
}
