<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-spacer />
            <RecordButton
               v-if="category.id"
               v-bind:disabled="isLoading || isPending"
               color="error"
               prepend-icon="$trash"
               @click="removeHandler">
               {{ t("app.delete") }}
            </RecordButton>
            <RecordButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               color="primary"
               prepend-icon="$save">
               {{ category.id ? t("app.update") : t("app.save") }}
            </RecordButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="flex-center text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.title"
                     v-bind:rules="[appRules.required()]">
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="category.content"
                     v-bind:rules="[appRules.required()]"
                     class="[&_.v-field\_\_input]:min-h-[min(var(--v-input-control-height,56px),364px)]"
                     auto-grow
                     no-resize>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="category.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ category.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import RecordButton from "@/components/Button/RecordButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import { ICategory, ICategoryStore, useCreateCategory, useGetCategoryById, useUpdateCategory, useDeleteCategory } from "../services/CategoryService";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const category = ref({
   is_active: 1
} as ICategory);
const routeId = computed(() => route.params.id);
const enabled = computed(() => !!routeId.value);

// services
const getCategoryById = useGetCategoryById({
   id: routeId,
   enabled: enabled,
   onSuccess: (data) => {
      category.value = { ...data };
   }
});
const updateCategory = useUpdateCategory();
const createCategory = useCreateCategory();
const deleteCategory = useDeleteCategory();

// status
const isLoading = computed(() => getCategoryById.isLoading.value);
const isPending = computed(() => createCategory.isPending.value || updateCategory.isPending.value);
const isError = computed(() => getCategoryById.isError.value);
const isSuccess = computed(() => createCategory.isSuccess.value);

// handlers
const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteRecord")
      });

      if (confirm) {
         await deleteCategory.mutateAsync(category.value.id, {
            onSuccess: () => {
               snackbarStore.add({ text: t("app.recordDeleted") });
               router.push({ name: "categoryList" });
            }
         });
      }
   } catch(data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: ICategoryStore = {
      code: category.value.code,
      title: category.value.title,
      content: category.value.content,
      is_active: category.value.is_active
   };

   try {
      if (!category.value.id) {
         await createCategory.mutateAsync(payload, {
            onSuccess: () => {
               router.push({ name: "categoryList" });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      } else {
         await updateCategory.mutateAsync({ id: category.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error, color: "error" });
   }
};
</script>
