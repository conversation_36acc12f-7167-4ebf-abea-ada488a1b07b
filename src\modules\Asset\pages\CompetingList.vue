<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
         </template>

         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading"
               v-bind:to="{ name: 'asset.competingCreate' }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("module.competingList") }}
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            @row:click="(item) => $router.push({ name: 'asset.competingDetail', params: { id: item.id } })">
            <template v-slot:item.category_list="{ item }">
               {{ item.category_list.map((category: any) => category.title).join(", ") }}
            </template>

            <template v-slot:item.manufacturer_list="{ item }">
               {{ item.manufacturer_list.map((manufacturer: any) => manufacturer.title).join(", ") }}
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetCompetingAll } from "../services/CompetingService";
import { ICompeting } from "../utils/types";

// hooks
const { t } = useI18n();

// states
const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<ICompeting>[] => [
   { title: t("app.title"), key: "title" },
   { title: t("module.category"), key: "category_list", width: "350" },
   { title: t("module.manufacturer"), key: "manufacturer_list", width: "150" }
]);

// services
const { data, isLoading } = useGetCompetingAll();
</script>
