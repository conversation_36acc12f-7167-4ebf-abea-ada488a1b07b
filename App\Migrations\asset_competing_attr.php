<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_competing_attr extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_competing_attr` (
         `attr_id` INT NOT NULL DEFAULT 0,
         `competing_id` INT NOT NULL DEFAULT 0,
         PRIMARY KEY (`attr_id`, `competing_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_competing_attr`");
   }
}
