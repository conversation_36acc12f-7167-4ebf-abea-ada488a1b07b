<template>
   <v-list-subheader
      v-if="props.item.itemType === 'subheader' && props.item.itemTitle"
      class="px-2 pb-2 font-semibold">
      {{ t(props.item.itemTitle as string, 2) }}
   </v-list-subheader>

   <v-divider
      v-else-if="props.item.itemType === 'divider'"
      class="mb-[7px]" />

   <v-list-item
      v-else
      v-bind="props.item.itemProps">
      <template v-slot:append="{ isOpen }">
         <v-icon
            v-if="props.item.children"
            v-bind:class="{ 'rotate-180': isOpen }"
            class="transition duration-200"
            icon="$dropdown"
            size="x-small" />
      </template>

      <template v-slot:prepend>
         <v-icon
            v-if="props.item.itemProps?.prependIcon"
            v-bind:icon="props.item.itemProps?.prependIcon"
            class="list-spacer-3" />
      </template>

      <v-list-item-title v-if="props.item.itemTitle">
         {{ t(props.item.itemTitle as string, 2) }}
      </v-list-item-title>
      <v-list-item-subtitle v-if="props.item.itemSubtitle">
         {{ t(props.item.itemSubtitle as string, 2) }}
      </v-list-item-subtitle>
   </v-list-item>
</template>

<script lang="ts" setup>
import type { TList } from "@/utils/vuetify";

const { t } = useI18n();
const props = defineProps({
   item: {
      type: Object as () => TList,
      required: true
   }
});
</script>
