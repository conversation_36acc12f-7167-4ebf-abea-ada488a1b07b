import {
   IconAlertSquareRoundedFilled,
   IconBell,
   IconBrowser,
   IconCalendar,
   IconCaretDownFilled,
   IconCaretRightFilled,
   IconCash,
   IconCheck,
   IconChevronDown,
   IconChevronLeft,
   IconChevronLeftPipe,
   IconChevronRight,
   IconChevronRightPipe,
   IconChevronUp,
   IconCircle,
   IconCircleCheckFilled,
   IconCircleDotFilled,
   IconCircleFilled,
   IconCircleX,
   IconClock,
   IconCloudUpload,
   IconColorPicker,
   IconCopy,
   IconCurrencyLira,
   IconDeviceFloppy,
   IconDotsVertical,
   IconEye,
   IconHeartHandshake,
   IconHome,
   IconInfoSquareRoundedFilled,
   IconLanguage,
   IconLink,
   IconListDetails,
   IconLogin,
   IconLogout,
   IconMenu2,
   IconMinus,
   IconMoonStars,
   IconPaperclip,
   IconPencil,
   IconPlus,
   IconQuestionMark,
   IconReceipt,
   IconRefresh,
   IconSearch,
   IconSelector,
   IconSettings,
   IconSlash,
   IconSortAscending,
   IconSortDescending,
   IconSparkles,
   IconSquareRounded,
   IconSquareRoundedCheckFilled,
   IconSquareRoundedMinusFilled,
   IconStar,
   IconStarFilled,
   IconStarHalfFilled,
   IconSun,
   IconTags,
   IconTools,
   IconTrash,
   IconTrendingUp,
   IconUser,
   IconUserCog,
   IconUserMinus,
   IconUserPlus,
   IconWallet,
   IconX
} from "@tabler/icons-vue";

const tablerAliases = {
   // vuetify default
   calendar: IconCalendar,
   cancel: IconCircleX,
   checkboxIndeterminate: IconSquareRoundedMinusFilled,
   checkboxOff: IconSquareRounded,
   checkboxOn: IconSquareRoundedCheckFilled,
   clear: IconX,
   close: IconX,
   collapse: IconChevronUp,
   complete: IconCheck,
   delete: IconCircleX,
   delimiter: IconCircleFilled,
   dropdown: IconCaretDownFilled,
   edit: IconPencil,
   error: IconCircleX,
   expand: IconChevronDown,
   eyeDropper: IconColorPicker,
   file: IconPaperclip,
   first: IconChevronLeftPipe,
   info: IconInfoSquareRoundedFilled,
   last: IconChevronRightPipe,
   loading: IconRefresh,
   menu: IconMenu2,
   minus: IconMinus,
   next: IconChevronRight,
   plus: IconPlus,
   prev: IconChevronLeft,
   radioOff: IconCircle,
   radioOn: IconCircleDotFilled,
   ratingEmpty: IconStar,
   ratingFull: IconStarFilled,
   ratingHalf: IconStarHalfFilled,
   sortAsc: IconSortAscending,
   sortDesc: IconSortDescending,
   subgroup: IconCaretDownFilled,
   success: IconCircleCheckFilled,
   treeviewCollapse: IconCaretDownFilled,
   treeviewExpand: IconCaretRightFilled,
   unfold: IconSelector,
   upload: IconCloudUpload,
   warning: IconAlertSquareRoundedFilled,
   sparkles: IconSparkles,
   save: IconDeviceFloppy,
   question: IconQuestionMark,
   calendarCheck: IconCalendar,
   notification: IconBell,
   slash: IconSlash,
   search: IconSearch,
   dots: IconDotsVertical,
   translate: IconLanguage,
   settings: IconSettings,
   trash: IconTrash,
   copy: IconCopy,
   link: IconLink,
   reset: IconRefresh,
   receipt: IconReceipt,
   handShake: IconHeartHandshake,
   browser: IconBrowser,
   clock: IconClock,
   alertTriangle: IconAlertSquareRoundedFilled,
   alertCircle: IconAlertSquareRoundedFilled,
   trendingUp: IconTrendingUp,
   eye: IconEye,

   // account
   accountProfile: IconUser,
   accountAdd: IconUserPlus,
   accountRemove: IconUserMinus,
   accountSettings: IconUserCog,
   accountLogin: IconLogin,
   accountLogout: IconLogout,

   // theme
   themeDark: IconMoonStars,
   themeLight: IconSun,

   // arrow
   chevronRight: IconChevronRight,
   chevronLeft: IconChevronLeft,

   // modules
   dashboard: IconHome,
   product: IconListDetails,
   category: IconBrowser,
   customer: IconUser,
   sale: IconDeviceFloppy,
   installment: IconCalendar,
   manufacturer: IconHeartHandshake,
   standard: IconTools,
   attribute: IconTags,
   user: IconUser,
   wallet: IconWallet,

   // money
   try: IconCurrencyLira,

   // dashboard
   addUser: IconUserPlus,
   getPayment: IconCash
};

const tabler = {
   component: (props: IconProps) => {
      return h(props.tag, [h(tablerAliases[props.icon as keyof typeof tablerAliases])]);
   }
};

export { tabler, tablerAliases };
