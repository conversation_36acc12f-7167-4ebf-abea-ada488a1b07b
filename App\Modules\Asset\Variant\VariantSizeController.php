<?php

declare(strict_types=1);

namespace App\Modules\Asset\Variant;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Variant\VariantSizeService;
use App\Modules\Asset\Variant\VariantSizeRequest;
use App\Modules\Asset\Variant\VariantSizeResponse;

/**
 * @OA\Tag(name="VariantSize", description="Varyant boyut işlemleri")
 */
class VariantSizeController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected VariantSizeService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"VariantSize"}, path="/variant-size/", summary="Varyant boyut listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllVariantSize() {
      $this->response(function () {
         $result = $this->service->getAll();

         return array_map(function ($item) {
            $response = new VariantSizeResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"VariantSize"}, path="/variant-size/{id}", summary="Varyant boyut detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getVariantSize(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id);

         $response = new VariantSizeResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Post(tags={"VariantSize"}, path="/variant-size/", summary="Varyant boyut ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"code", "title", "variant", "value"},
    *       @OA\Property(property="code", type="string", example="SIZE-L"),
    *       @OA\Property(property="title", type="string", example="Large"),
    *       @OA\Property(property="variant", type="string", example="size"),
    *       @OA\Property(property="value", type="string", example="L")
    *    ))
    * )
    */
   public function createVariantSize() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new VariantSizeRequest();
         $request->fromArray($json);
         $result = $this->service->createVariantSize($request);

         $response = new VariantSizeResponse();
         $response->fromArray($result);

         return $response;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"VariantSize"}, path="/variant-size/", summary="Varyant boyut güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "code", "title", "variant", "value"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="SIZE-L"),
    *       @OA\Property(property="title", type="string", example="Large"),
    *       @OA\Property(property="variant", type="string", example="size"),
    *       @OA\Property(property="value", type="string", example="L")
    *    ))
    * )
    */
   public function updateVariantSize() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new VariantSizeRequest();
         $request->fromArray($json);
         $result = $this->service->updateVariantSize($request);

         $response = new VariantSizeResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"VariantSize"}, path="/variant-size/{id}", summary="Varyant boyut sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteVariantSize(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'id' => $id,
            'deleted_at' => ['IS NULL']
         ]);

         return $result;
      });
   }
}