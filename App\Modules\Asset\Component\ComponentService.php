<?php

declare(strict_types=1);

namespace App\Modules\Asset\Component;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Asset\Component\ComponentRequest;
use App\Modules\Asset\Component\ComponentRepository;

class ComponentService extends BaseService {
   /** @var ComponentRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ComponentRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAll(): array {
      $result = $this->repository->findAll();

      return array_map(function ($item) {
         $item['manufacturer'] = $this->repository->findManufacturer($item['manufacturer_id']);
         return $item;
      }, $result);
   }

   public function createComponent(ComponentRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->validate($request->toArray(), [
            'title' => 'required',
            'note' => 'nullable',
            'model' => 'nullable',
            'quantity' => 'required|numeric',
            'min_quantity' => 'required|numeric',
            'serial' => 'nullable',
            'manufacturer_id' => 'required|numeric',
            'location_id' => 'required|numeric',
            'supplier_id' => 'required|numeric',
            'order_serial' => 'nullable',
            'order_number' => 'nullable',
            'purchase_date' => 'nullable',
            'purchase_price' => 'numeric',
            'purchase_currency' => 'nullable',
            'warranty_duration' => 'nullable',
            'warranty_type' => 'nullable',
            'image_path' => 'nullable'
         ]);

         $id = $this->create([
            'title' => $request->title,
            'note' => $request->note,
            'model' => $request->model,
            'quantity' => $request->quantity,
            'min_quantity' => $request->min_quantity,
            'serial' => $request->serial,
            'manufacturer_id' => $request->manufacturer_id,
            'location_id' => $request->location_id,
            'supplier_id' => $request->supplier_id,
            'order_serial' => $request->order_serial,
            'order_number' => $request->order_number,
            'purchase_date' => $request->purchase_date,
            'purchase_price' => $request->purchase_price,
            'purchase_currency' => $request->purchase_currency,
            'warranty_duration' => $request->warranty_duration,
            'warranty_type' => $request->warranty_type
         ]);

         if (isset($request->image_path) && is_array($request->image_path)) {
            foreach ($request->image_path as $path) {
               $this->create([
                  'component_id' => $id,
                  'image_path' => $path
               ], 'asset_component_image');
            }
         }

         return $this->getOne($id);
      });
   }

   public function updateComponent(ComponentRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'title' => 'required',
            'note' => 'nullable',
            'model' => 'nullable',
            'quantity' => 'required|numeric',
            'min_quantity' => 'required|numeric',
            'serial' => 'nullable',
            'manufacturer_id' => 'required|numeric',
            'location_id' => 'required|numeric',
            'supplier_id' => 'required|numeric',
            'order_serial' => 'nullable',
            'order_number' => 'nullable',
            'purchase_date' => 'nullable',
            'purchase_price' => 'numeric',
            'purchase_currency' => 'nullable',
            'warranty_duration' => 'nullable',
            'warranty_type' => 'nullable',
            'image_path' => 'nullable'
         ]);

         $this->update($request, [
            'title' => $request->title,
            'note' => $request->note,
            'model' => $request->model,
            'quantity' => $request->quantity,
            'min_quantity' => $request->min_quantity,
            'serial' => $request->serial,
            'manufacturer_id' => $request->manufacturer_id,
            'location_id' => $request->location_id,
            'supplier_id' => $request->supplier_id,
            'order_serial' => $request->order_serial,
            'order_number' => $request->order_number,
            'purchase_date' => $request->purchase_date,
            'purchase_price' => $request->purchase_price,
            'purchase_currency' => $request->purchase_currency,
            'warranty_duration' => $request->warranty_duration,
            'warranty_type' => $request->warranty_type
         ], [
            'id' => $request->id
         ]);

         if (isset($request->image_path) && is_array($request->image_path)) {
            // $this->repository->hardDelete([
            //    'component_id' => $request->id
            // ], 'asset_component_image');

            foreach ($request->image_path as $path) {
               $this->create([
                  'component_id' => $request->id,
                  'image_path' => $path
               ], 'asset_component_image');
            }
         }

         return $this->getOne($request->id);
      });
   }
}
