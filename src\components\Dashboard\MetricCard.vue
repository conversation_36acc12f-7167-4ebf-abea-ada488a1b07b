<template>
   <v-card
      class="metric-card"
      :class="`metric-card--${color}`">
      <v-card-text class="pa-4">
         <div class="d-flex align-center justify-space-between mb-3">
            <div class="metric-icon">
               <v-icon
                  :icon="icon"
                  :color="color"
                  size="24" />
            </div>
            <div class="text-right">
               <div class="metric-value text-h5 font-weight-bold">
                  {{ value }}
               </div>
               <div class="metric-subtitle text-caption text-medium-emphasis">
                  {{ subtitle }}
               </div>
            </div>
         </div>
         <div class="metric-title text-body-2 font-weight-medium text-medium-emphasis">
            {{ title }}
         </div>
      </v-card-text>
   </v-card>
</template>

<script lang="ts" setup>
interface Props {
   title: string;
   value: string;
   subtitle?: string;
   icon: string;
   color?: "primary" | "secondary" | "success" | "warning" | "error" | "info";
}

withDefaults(defineProps<Props>(), {
   subtitle: "",
   color: "primary"
});
</script>

<style scoped>
.metric-card {
   transition: all 0.3s ease;
   border-left: 4px solid transparent;
}

.metric-card:hover {
   transform: translateY(-2px);
   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.metric-card--primary {
   border-left-color: rgb(var(--v-theme-primary));
}

.metric-card--secondary {
   border-left-color: rgb(var(--v-theme-secondary));
}

.metric-card--success {
   border-left-color: rgb(var(--v-theme-success));
}

.metric-card--warning {
   border-left-color: rgb(var(--v-theme-warning));
}

.metric-card--error {
   border-left-color: rgb(var(--v-theme-error));
}

.metric-card--info {
   border-left-color: rgb(var(--v-theme-info));
}

.metric-icon {
   background: rgba(var(--v-theme-surface-variant), 0.1);
   border-radius: 50%;
   width: 48px;
   height: 48px;
   display: flex;
   align-items: center;
   justify-content: center;
}

.metric-value {
   line-height: 1.2;
}

.metric-subtitle {
   line-height: 1.2;
   margin-top: 2px;
}

.metric-title {
   margin-top: 8px;
}
</style>