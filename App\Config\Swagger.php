<?php

declare(strict_types=1);

namespace App\Config;

use System\Router\Router;
use App\Core\Middlewares\Swagger as Middleware;
use App\Modules\Asset\Swagger\SwaggerController as Asset;
use App\Modules\Website\Swagger\SwaggerController as Website;

class Swagger {
   public function __construct(
      private Router $router
   ) {
   }

   public function run(): void {
      $this->router->prefix('swagger')->middleware([Middleware::class])->group(function () {
         // swagger index
         $this->router->get('/', function () {
            require_once ROOT_DIR . '/Public/swagger/index.html';
         });

         // swagger list
         $this->router->get('/list', function () {
            header('Content-Type: application/json; charset=UTF-8');
            print(json_encode([
               ['url' => './swagger/asset', 'name' => 'Asset'],
               ['url' => './swagger/website', 'name' => 'Website']
            ]));
         });

         // swagger json
         $this->router->get('/asset', [Asset::class, 'json']);
         $this->router->get('/website', [Website::class, 'json']);
      });
   }
}
