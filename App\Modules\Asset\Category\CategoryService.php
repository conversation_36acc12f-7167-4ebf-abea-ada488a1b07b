<?php

declare(strict_types=1);

namespace App\Modules\Asset\Category;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Asset\Category\CategoryRequest;
use App\Modules\Asset\Category\CategoryRepository;

class CategoryService extends BaseService {
   /** @var CategoryRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      CategoryRepository $repository,
   ) {
      $this->repository = $repository;
   }

   // override
   public function getAll(int $lang_id = 1): array {
      $result = $this->repository->findAll($lang_id);

      return $result;
   }

   // override
   public function getOne(int $id, int $lang_id = 1): array {
      $result = $this->repository->findOne($id, $lang_id);

      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createCategory(CategoryRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->validate($request->toArray(), [
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'group_id' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $id = $this->create([
            'code' => $request->code,
            'image_path' => $request->image_path,
            'is_active' => $request->is_active,
            'sort_order' => $request->sort_order,
            'parent_id' => $request->parent_id,
            'group_id' => $request->group_id
         ]);

         $this->translate($request->translate, [
            'category_id' => $id
         ], 'asset_category_translate');

         return $this->getOne($id, $lang_id);
      });
   }

   public function updateCategory(CategoryRequest $request, int $lang_id): array {
      return $this->transaction(function () use ($request, $lang_id) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'group_id' => 'required|numeric',
            'translate' => 'required|must_be_array'
         ]);

         $this->validate($request->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required'
         ]);

         $this->update($request, [
            'code' => $request->code,
            'image_path' => $request->image_path,
            'is_active' => $request->is_active,
            'sort_order' => $request->sort_order,
            'parent_id' => $request->parent_id,
            'group_id' => $request->group_id
         ], [
            'id' => $request->id
         ]);

         $this->translate($request->translate, [
            'category_id' => $request->id
         ], 'asset_category_translate');

         return $this->getOne($request->id, $lang_id);
      });
   }
}
