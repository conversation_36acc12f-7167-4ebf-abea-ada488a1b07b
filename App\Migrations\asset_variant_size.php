<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_variant_size extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_variant_size` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `code` VARCHAR(150) NOT NULL,
         `title` VARCHAR(250) NOT NULL,
         `variant` INT NOT NULL DEFAULT 0,
         `value` VARCHAR(100) NOT NULL,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_variant_size`");
   }
}