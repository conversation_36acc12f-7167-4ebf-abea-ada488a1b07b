<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading && isFirst">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               color="primary"
               prepend-icon="$reset"
               @click="resetHandler">
               {{ t("app.reset") }}
            </ActionButton>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <template v-slot:items>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.code"
                     disabled />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="product.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="product.content"
                     class="max-grow-80"
                     auto-grow
                     disabled
                     no-resize />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="product.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ product.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList v-bind:items="product.image_list" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:title>Bağlantılar</template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("module.category") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="product.category_list"
                     v-bind:items="categoryAll"
                     v-bind:loading="categoryLoading"
                     v-bind:rules="[appRules.required()]"
                     item-value="id"
                     multiple
                     return-object />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("module.manufacturer") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <div class="mb-[22px] flex h-9 items-center text-sm select-all px-2">
                     {{ product.manufacturer_list.map((item) => item.title).join(", ") }}
                  </div>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("module.standard") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-row no-gutters>
                     <v-col cols="12">
                        <DetailList
                           v-bind:data="product.standard_list"
                           v-bind:to="'/asset/standard/'"
                           title="title"
                           subtitle="value" />
                     </v-col>
                  </v-row>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:title>SEO</template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.url") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.url"
                     v-bind:rules="[appRules.required()]">
                     <template v-slot:append-inner>
                        <URLButton
                           v-model="product.url"
                           v-bind:source="product.title" />
                        <TranslateButton
                           v-if="language !== 1"
                           v-model="product.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.metaTitle") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="product.meta_title" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.metaDescription") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="product.meta_description"
                     v-bind:rules="[appRules.required()]"
                     class="max-grow-80"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="product.meta_description" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.metaKeywords") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="product.meta_keywords" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import URLButton from "@/components/Button/URLButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import DetailList from "@/components/List/DetailList.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { appRules } from "@/utils/rules";
import { useGetCategoryAll } from "../services/CategoryService";
import { useDeleteProduct, useGetProductById, useOverrideProduct } from "../services/ProductService";
import { IProduct, IProductStore } from "../utils/types";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const snackbarStore = useSnackbarStore();

// states
const product = ref({
   is_active: 1,
   standard_list: [] as any,
   category_list: [] as any,
   manufacturer_list: [] as any,
   attr_list: [] as any
} as IProduct);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);
const language = ref(1);

// services
const getProductById = useGetProductById({
   enabled: isEdit,
   params: {
      id: routeId,
      language: language
   },
   onSuccess: (item: IProduct) => {
      product.value = { ...item };
   }
});
const overrideProduct = useOverrideProduct();
const deleteProduct = useDeleteProduct();

// relation services
const { data: categoryAll, isLoading: categoryLoading } = useGetCategoryAll();

// status
const isLoading = computed(() => getProductById.isLoading.value);
const isFirst = computed(() => getProductById.isFirst.value);
const isPending = computed(() => overrideProduct.isPending.value);
const isError = computed(() => getProductById.isError.value);

// handlers
const resetHandler = async () => {
   try {
      await deleteProduct.mutateAsync({ id: product.value.id });
      snackbarStore.add({ text: t("app.recordReset") });
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};

const formHandler = async () => {
   const payload: IProductStore = {
      product_id: product.value.id,
      is_active: product.value.is_active,
      sort_order: product.value.sort_order,
      translate: [
         {
            language_id: language.value,
            title: product.value.title,
            url: product.value.url,
            meta_title: product.value.meta_title,
            meta_description: product.value.meta_description,
            meta_keywords: product.value.meta_keywords
         }
      ],
      product_category: product.value.category_list.map((item) => item.id)
   };

   try {
      await overrideProduct.mutateAsync(payload);
      snackbarStore.add({ text: t("app.recordUpdated") });
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
