<template>
   <RecordDialog
      ref="recordDialog"
      @after-leave="reset">
      <v-row no-gutters>
         <v-col md="12">
            <v-row>
               <v-col md="8">
                  <v-list-subheader>{{ t("app.customer") }}</v-list-subheader>
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.saleDate") }}</v-list-subheader>
               </v-col>
            </v-row>
         </v-col>

         <v-col md="12">
            <v-row>
               <v-col md="8">
                  <SelectInput
                     v-model="sale.customer"
                     v-bind:items="customerAll"
                     v-bind:loading="customerLoading"
                     v-bind:rules="[appRules.required()]"
                     item-title="name"
                     item-value="id"
                     return-object
                     search-deep>
                     <template v-slot:title="{ item }">{{ item.raw.name }} {{ item.raw.surname }}</template>
                     <template v-slot:subtitle="{ item }">
                        <div class="flex justify-between">
                           <div>{{ item.raw.tckn }}</div>
                           <div>{{ item.raw.phone }}</div>
                        </div>
                     </template>
                     <template v-slot:selection="{ item }">{{ item.raw.name }} {{ item.raw.surname }}</template>
                  </SelectInput>
               </v-col>
               <v-col md="4">
                  <DatePicker
                     v-model="sale.sale_date"
                     v-bind:rules="[appRules.required()]"
                     v-bind:title="true" />
               </v-col>
            </v-row>
         </v-col>

         <v-col md="12">
            <v-row>
               <v-col md="5">
                  <v-list-subheader>{{ t("app.product") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.quantity") }}</v-list-subheader>
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
               </v-col>
            </v-row>
         </v-col>
         <v-col md="12">
            <v-row>
               <v-col md="5">
                  <SelectInput
                     v-model="sale.product_list"
                     v-model:filterSearch="productTemp"
                     v-bind:items="productAll"
                     v-bind:loading="productLoading"
                     item-title="title"
                     item-value="id"
                     return-object
                     @update:model-value="selectProductHandler($event)">
                     <template v-slot:no-data-append>
                        <v-btn
                           prepend-icon="$plus"
                           variant="text"
                           @click="newProductHandler">
                           {{ t("app.add") }}
                        </v-btn>
                     </template>
                  </SelectInput>
               </v-col>
               <v-col md="3">
                  <v-number-input
                     v-model="quantity"
                     v-bind:clearable="false"
                     v-bind:disabled="!sale.product_list"
                     v-bind:min="1"
                     control-variant="split" />
               </v-col>
               <v-col md="4">
                  <NumberInput
                     v-model="price"
                     v-bind:disabled="!sale.product_list"
                     v-bind:step="10.0">
                     <template v-slot:append>
                        <v-btn
                           v-bind:disabled="!sale.product_list || !quantity"
                           color="primary"
                           density="default"
                           variant="tonal"
                           @click="addProductHandler">
                           {{ t("app.add") }}
                        </v-btn>
                     </template>
                  </NumberInput>
               </v-col>
            </v-row>
         </v-col>
         <v-col md="12">
            <div class="border-thin m-4 overflow-hidden rounded-md">
               <DataTable
                  v-bind:headers="productHeaders"
                  v-bind:items="productList"
                  v-bind:items-per-page="-1"
                  v-bind:sticky-header="false"
                  accent-header
                  disable-sort
                  hide-default-footer>
                  <template v-slot:item.total="{ item }">{{ formatNumber(Number(item.price) * item.quantity) }} ₺</template>

                  <template v-slot:item.actions="{ index }">
                     <ActionButton
                        icon="$close"
                        @click.stop="removeProductHandler(index)" />
                  </template>

                  <template v-slot:append>
                     <tr>
                        <td colspan="3">
                           {{ t("app.total") }}
                        </td>
                        <td class="font-semibold">
                           {{ productList.reduce((sum, product) => sum + product.quantity, 0) }}
                        </td>
                        <td class="text-right font-semibold">{{ formatNumber(productList.reduce((sum, product) => sum + Number(product.price) * product.quantity, 0)) }} ₺</td>
                        <td></td>
                     </tr>
                  </template>
               </DataTable>
            </div>
         </v-col>

         <v-col md="12">
            <v-row>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.prePayment") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.installment") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.installmentType") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.installmentStartDate") }}</v-list-subheader>
               </v-col>
            </v-row>
         </v-col>

         <v-col md="12">
            <v-row>
               <v-col md="3">
                  <NumberInput
                     v-model="sale.pre_payment"
                     v-bind:disabled="!productList.length"
                     v-bind:rules="[appRules.required()]" />
               </v-col>
               <v-col md="3">
                  <v-number-input
                     v-model="sale.installment"
                     v-bind:disabled="!productList.length"
                     v-bind:min="1" />
               </v-col>
               <v-col md="3">
                  <v-select
                     v-model="sale.installment_type"
                     v-bind:disabled="!productList.length || sale.installment <= 1"
                     v-bind:items="installmentTypes" />
               </v-col>
               <v-col md="3">
                  <DatePicker
                     v-model="sale.installment_start_date"
                     v-bind:disabled="!productList.length || sale.installment <= 1"
                     v-bind:title="t('app.installmentStartDate')" />
               </v-col>
            </v-row>
         </v-col>

         <v-col md="12">
            <v-list-subheader>{{ t("app.notes") }}</v-list-subheader>
         </v-col>
         <v-col md="12">
            <v-textarea
               v-model="sale.notes"
               auto-grow
               no-resize />
         </v-col>
      </v-row>
   </RecordDialog>

   <ProductDialog ref="productDialog" />
</template>

<script lang="ts" setup>
import { useGetCustomerAll } from "@/services/CustomerService";
import { IProduct, useGetProductAll } from "@/services/ProductService";
import type { ISale, ISaleStore } from "@/services/SaleService";
import { useCreateSale, useGetSaleById, useUpdateSale } from "@/services/SaleService";
import ActionButton from "../Button/ActionButton.vue";
import NumberInput from "../Input/NumberInput.vue";
import SelectInput from "../Input/SelectInput.vue";
import RecordDialog from "../Layout/Default/Dialog/RecordDialog.vue";
import DataTable from "../Table/DataTable.vue";
import ProductDialog from "./ProductDialog.vue";
import DatePicker from "../Input/DatePicker.vue";

// hooks
const { t } = useI18n();
const snackbarStore = useSnackbarStore();

// initials
const saleInitial = {
   installment: 1,
   installment_type: "monthly",
   notes: "",
   pre_payment: 0,
   sale_date: new Date() as any,
   installment_start_date: new Date() as any
} as ISale;

// states
const recordDialog = ref<InstanceType<typeof RecordDialog>>();
const productDialog = ref<InstanceType<typeof ProductDialog>>();
const sale = ref({ ...saleInitial });
const dialogId = computed(() => sale.value.id);
const enabled = computed(() => !!dialogId.value);
const installmentTypes = computed(() => [
   { title: t("app.monthly"), value: "monthly" },
   { title: t("app.weekly"), value: "weekly" }
]);
const quantity = ref(0);
const price = ref(0);
const total = computed(() => formatNumber(price.value * quantity.value));
const productList = ref([] as any[]);
const productTemp = ref();

const productHeaders = computed((): THeader<any>[] => [
   { title: t("app.code"), key: "product.code", width: "100" },
   { title: t("app.title"), key: "product.title" },
   { title: t("app.price"), key: "price", width: "150", money: "₺", align: "end" },
   { title: t("app.quantity"), key: "quantity", width: "100" },
   { title: t("app.total"), key: "total", width: "200", align: "end" },
   { key: "actions" }
]);

// services
const { isLoading } = useGetSaleById({
   id: dialogId,
   enabled: enabled,
   onSuccess: (item) => {
      sale.value = { ...item };
   }
});
const createSale = useCreateSale();
const updateSale = useUpdateSale();

// relation services
const { data: customerAll, isLoading: customerLoading } = useGetCustomerAll();
const { data: productAll, isLoading: productLoading } = useGetProductAll();

// handlers
const open = async (item?: ISale) => {
   try {
      sale.value.id = item?.id || 0;

      const confirm = await recordDialog.value?.open({
         width: 1000,
         title: item?.id ? t("app.detailRecord") : t("app.createRecord"),
         loading: isLoading
      });

      function dateFormat(d: string): string {
         // d.toISOString().split('T')[0]+' '+d.toTimeString().split(' ')[0];
         const date = new Date(d);
         const base = date.toLocaleString("sv-SE", { hour12: false }).replace("T", " ");
         const ms = date.getMilliseconds().toString().padStart(3, "0");
         return `${base}.${ms}`;
      }

      if (confirm) {
         const payload: ISaleStore = {
            customer_id: sale.value.customer.id,
            total_amount: productList.value.reduce((sum, product) => sum + Number(product.price) * product.quantity, 0),
            sale_product: productList.value.map((item) => ({
               id: item.product.id,
               quantity: item.quantity,
               unit_price: item.price
            })),
            debit: productList.value.reduce((sum, product) => sum + Number(product.price) * product.quantity, 0) - sale.value.pre_payment,
            pre_payment: sale.value.pre_payment,
            installment: sale.value.installment,
            installment_type: sale.value.installment_type,
            notes: sale.value.notes,
            sale_date: dateFormat(sale.value.sale_date),
            installment_start_date: dateFormat(sale.value.installment_start_date)
         };

         if (!item?.id) {
            await createSale.mutateAsync(payload);
            snackbarStore.add({ text: t("app.recordCreated") });
         } else {
            await updateSale.mutateAsync(payload);
            snackbarStore.add({ text: t("app.recordUpdated") });
         }
      }
   } catch (data: any) {
      snackbarStore.add({ text: data.error || data, color: "error" });
   } finally {
      recordDialog.value?.close();
   }
};

const reset = () => {
   sale.value = { ...saleInitial };
   productList.value = [];
   productTemp.value = "";
};

const newProductHandler = () => {
   productDialog.value?.open(null as any, productTemp.value);
};

const selectProductHandler = (item: IProduct) => {
   if (item) {
      quantity.value = 1;
      price.value = item.price;
   } else {
      quantity.value = 0;
      price.value = 0;
   }
};

const addProductHandler = () => {
   productList.value.push({
      product: sale.value.product_list,
      quantity: quantity.value,
      price: price.value,
      total: total.value
   });

   sale.value.product_list = null as any;
   quantity.value = 0;
   price.value = 0;
};

const removeProductHandler = (index: number) => {
   productList.value.splice(index, 1);
};

defineExpose({ open, isLoading });
</script>
