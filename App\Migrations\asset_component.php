<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_component extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_component` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `title` VARCHAR(250) NOT NULL,
         `note` TEXT NULL DEFAULT NULL,
         `model` VARCHAR(100) NULL DEFAULT NULL,
         `quantity` INT NOT NULL DEFAULT 0,
         `min_quantity` INT NOT NULL DEFAULT 0,
         `serial` VARCHAR(100) NULL DEFAULT NULL,
         `manufacturer_id` INT NOT NULL DEFAULT 0,
         `location_id` INT NOT NULL DEFAULT 0,
         `supplier_id` INT NOT NULL DEFAULT 0,
         `order_serial` VARCHAR(100) NULL DEFAULT NULL,
         `order_number` VARCHAR(100) NULL DEFAULT NULL,
         `purchase_date` DATE NULL DEFAULT NULL,
         `purchase_price` DECIMAL(10, 2) NOT NULL DEFAULT 0,
         `purchase_currency` VARCHAR(10) NOT NULL DEFAULT 'TRY',
         `warranty_duration` INT NOT NULL DEFAULT 0,
         `warranty_type` ENUM('day','month','year') NOT NULL DEFAULT 'year',
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_component`");
   }
}
