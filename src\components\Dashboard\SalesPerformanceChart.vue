<template>
   <v-card elevation="2" class="sales-performance-chart">
      <v-card-title class="d-flex align-center justify-space-between">
         <div>
            <h3 class="text-h6 font-weight-bold">{{ t("app.salesPerformance") }}</h3>
         </div>
         <v-btn-toggle
            v-model="selectedPeriod"
            mandatory
            variant="outlined"
            size="small"
            density="compact">
            <v-btn value="daily" size="small">{{ t("app.daily") }}</v-btn>
            <v-btn value="monthly" size="small">{{ t("app.monthly") }}</v-btn>
            <v-btn value="yearly" size="small">{{ t("app.yearly") }}</v-btn>
         </v-btn-toggle>
      </v-card-title>

      <v-card-text>
         <div v-if="loading" class="d-flex justify-center align-center" style="height: 300px;">
            <v-progress-circular indeterminate color="primary" />
         </div>

         <div v-else-if="chartData.length === 0" class="d-flex justify-center align-center" style="height: 300px;">
            <div class="text-center">
               <v-icon icon="$chart-line" size="48" color="grey-lighten-1" class="mb-2" />
               <p class="text-body-2 text-medium-emphasis">{{ t("app.noSalesData") }}</p>
            </div>
         </div>

         <div v-else>
            <canvas ref="chartCanvas" style="max-height: 300px;"></canvas>
         </div>
      </v-card-text>
   </v-card>
</template>

<script lang="ts" setup>
import type { ISale } from '@/services/SaleService';
import { Chart, registerables } from 'chart.js';
import { toRaw } from 'vue'; // toRaw import et

Chart.register(...registerables);

interface Props {
   salesData: ISale[];
   loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
   loading: false
});

const { t } = useI18n();
const chartCanvas = ref<HTMLCanvasElement | null>(null);
const chartInstance = ref<Chart | null>(null);
const selectedPeriod = ref<'daily' | 'monthly' | 'yearly'>('monthly');

// Computed chart data based on selected period
const chartData = computed(() => {
   if (!props.salesData || props.salesData.length === 0) return [];

   const data = [...props.salesData].sort((a, b) =>
      new Date(a.created_at || '').getTime() - new Date(b.created_at || '').getTime()
   );

   switch (selectedPeriod.value) {
      case 'daily':
         return getDailyData(data);
      case 'monthly':
         return getMonthlyData(data);
      case 'yearly':
         return getYearlyData(data);
      default:
         return getMonthlyData(data);
   }
});

// Data processing functions
function getDailyData(sales: ISale[]) {
   const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toISOString().split('T')[0];
   });

   const dailyTotals = last30Days.map(date => {
      const dayTotal = sales
         .filter(sale => sale.created_at?.startsWith(date))
         .reduce((sum, sale) => sum + parseFloat(sale.price || '0'), 0);

      return {
         label: new Date(date).toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit' }),
         value: dayTotal
      };
   });

   return dailyTotals;
}

function getMonthlyData(sales: ISale[]) {
   const last12Months = Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (11 - i));
      return {
         year: date.getFullYear(),
         month: date.getMonth() + 1
      };
   });

   const monthlyTotals = last12Months.map(({ year, month }) => {
      const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
      const monthTotal = sales
         .filter(sale => sale.created_at?.startsWith(monthStr))
         .reduce((sum, sale) => sum + parseFloat(sale.price || '0'), 0);

      return {
         label: new Date(year, month - 1).toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' }),
         value: monthTotal
      };
   });

   return monthlyTotals;
}

function getYearlyData(sales: ISale[]) {
   const years = [...new Set(sales.map(sale =>
      new Date(sale.created_at || '').getFullYear()
   ))].sort();

   const yearlyTotals = years.map(year => {
      const yearTotal = sales
         .filter(sale => new Date(sale.created_at || '').getFullYear() === year)
         .reduce((sum, sale) => sum + parseFloat(sale.price || '0'), 0);

      return {
         label: year.toString(),
         value: yearTotal
      };
   });

   return yearlyTotals;
}

// Chart creation and update
function createChart() {
   if (!chartCanvas.value || chartData.value.length === 0) return;

   const ctx = chartCanvas.value.getContext('2d');
   if (!ctx) return;

   // Destroy existing chart
   if (chartInstance.value) {
      chartInstance.value.destroy();
   }

   // Veriyi deep clone ile tamamen kopyala
   const rawChartData = toRaw(chartData.value);
   const clonedData = structuredClone(rawChartData);

   chartInstance.value = new Chart(ctx, {
      type: 'line',
      data: {
         labels: clonedData.map(item => item.label),
         datasets: [{
            label: t('app.salesAmount'),
            data: clonedData.map(item => item.value),
            borderColor: 'rgb(var(--v-theme-primary))',
            backgroundColor: 'rgba(var(--v-theme-primary), 0.1)',
            borderWidth: 2,
            // fill: true yerine fill özelliğini kaldır veya false yap
            fill: false,
            tension: 0.4,
            pointBackgroundColor: 'rgb(var(--v-theme-primary))',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 4,
            pointHoverRadius: 6
         }]
      },
      options: {
         responsive: true,
         maintainAspectRatio: false,
         plugins: {
            legend: {
               display: false
            },
            tooltip: {
               backgroundColor: 'rgba(0, 0, 0, 0.8)',
               titleColor: '#fff',
               bodyColor: '#fff',
               borderColor: 'rgb(var(--v-theme-primary))',
               borderWidth: 1,
               callbacks: {
                  label: function(context) {
                     const value = context.parsed.y;
                     return `${t('app.salesAmount')}: ${formatCurrency(value)}`;
                  }
               }
            }
         },
         scales: {
            x: {
               grid: {
                  display: false
               },
               ticks: {
                  color: 'rgb(var(--v-theme-on-surface-variant))'
               }
            },
            y: {
               beginAtZero: true,
               grid: {
                  color: 'rgba(var(--v-theme-outline), 0.2)'
               },
               ticks: {
                  color: 'rgb(var(--v-theme-on-surface-variant))',
                  callback: function(value) {
                     return formatCurrency(value as number);
                  }
               }
            }
         },
         interaction: {
            intersect: false,
            mode: 'index'
         }
      }
   });
}

function formatCurrency(value: number) {
   return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
   }).format(value);
}

// Watchers
watch([chartData, () => props.loading], () => {
   if (!props.loading) {
      nextTick(() => {
         createChart();
      });
   }
}, { immediate: true });

watch(selectedPeriod, () => {
   nextTick(() => {
      createChart();
   });
});

// Cleanup
onUnmounted(() => {
   if (chartInstance.value) {
      chartInstance.value.destroy();
   }
});
</script>

<style scoped>
.sales-performance-chart {
   height: 100%;
}
</style>