<?php

declare(strict_types=1);

namespace App\Modules\Asset\Variant;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Asset\Variant\VariantSizeRequest;
use App\Modules\Asset\Variant\VariantSizeRepository;

class VariantSizeService extends BaseService {
   /** @var VariantSizeRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      VariantSizeRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function createVariantSize(VariantSizeRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->validate($request->toArray(), [
            'code' => 'required',
            'title' => 'required',
            'variant' => 'required',
            'value' => 'required'
         ]);

         $id = $this->create([
            'code' => $request->code,
            'title' => $request->title,
            'variant' => $request->variant,
            'value' => $request->value,
         ]);

         return $this->getOne($id);
      });
   }

   public function updateVariantSize(VariantSizeRequest $request): array {
      return $this->transaction(function () use ($request) {
         $this->check([
            'id' => $request->id
         ]);

         $this->validate($request->toArray(), [
            'id' => 'required|numeric',
            'code' => 'required',
            'title' => 'required',
            'variant' => 'required',
            'value' => 'required'
         ]);

         $this->update($request, [
            'code' => $request->code,
            'title' => $request->title,
            'variant' => $request->variant,
            'value' => $request->value,
         ], [
            'id' => $request->id
         ]);

         return $this->getOne($request->id);
      });
   }
}
