<?php

declare(strict_types=1);

use System\Migration\Migration;

class asset_unit extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `asset_unit` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `title` VARCHAR(250) NOT NULL,
         `type` INT NOT NULL DEFAULT 0,
         `intl_id` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('asset_unit')->insert([
         'title' => 'Kilogram',
         'type' => 1,
         'intl_id' => 1,
      ])->prepare()->execute();

      $this->database->table('asset_unit')->insert([
         'title' => 'Metre',
         'type' => 2,
         'intl_id' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `asset_unit`");
   }
}