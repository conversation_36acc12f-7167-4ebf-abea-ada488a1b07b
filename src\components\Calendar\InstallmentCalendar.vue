<template>
   <Card v-bind:loading="isLoading" class="installment-calendar-card">
      <template v-slot:header>
         <v-card-title class="flex-center p-0 text-base">{{ t("app.installmentCalendar") }}</v-card-title>
         <div class="d-flex align-center gap-2">
            <v-btn
               icon="$chevronLeft"
               size="small"
               variant="text"
               @click="previousWeek" />
            <div class="text-subtitle-1 font-weight-medium min-w-32 text-center">
               {{ currentWeekLabel }}
            </div>
            <v-btn
               icon="$chevronRight"
               size="small"
               variant="text"
               @click="nextWeek" />
            <!-- Mevcut haftaya dön butonu -->
            <v-btn
               v-if="!isCurrentWeek"
               icon="$refresh"
               size="small"
               variant="text"
               @click="goToCurrentWeek"
               :title="t('app.goToCurrentWeek')">
            </v-btn>
         </div>
      </template>

      <v-card-text class="pa-0">
         <!-- Takvim Grid -->
         <div class="calendar-container">
            <!-- Gün ba<PERSON>kları -->
            <div class="calendar-header">
               <div
                  v-for="day in weekDays"
                  :key="day"
                  class="calendar-day-header">
                  {{ day }}
               </div>
            </div>

            <!-- Takvim günleri (sadece 7 gün) -->
            <div class="calendar-body weekly-view">
               <div
                  v-for="(day, index) in weekDaysData"
                  :key="index"
                  class="calendar-day"
                  :class="{
                     'calendar-day--today': day.isToday,
                     'calendar-day--has-installments': day.installments.length > 0,
                     'calendar-day--overdue': day.hasOverdue,
                     'calendar-day--paid': day.hasPaid,
                     'calendar-day--pending': day.hasPending
                  }"
                  @click="showDayDetails(day)">
                  <!-- Gün numarası ve tarih -->
                  <div class="calendar-day-header-info">
                     <div class="calendar-day-number">
                        {{ day.date.getDate() }}
                        <div v-if="day.isToday" class="calendar-today-indicator"></div>
                     </div>
                     <div class="calendar-day-month">
                        {{ day.date.toLocaleDateString('tr-TR', { month: 'short' }) }}
                     </div>
                  </div>

                  <!-- Taksitler -->
                  <div class="calendar-installments">
                     <div
                        v-for="installment in day.installments.slice(0, 3)"
                        :key="installment.id"
                        class="calendar-installment"
                        :class="`calendar-installment--${installment.status}`"
                        @click.stop="$router.push({ name: 'installmentDetail', params: { id: installment.id } })">
                        <div class="calendar-installment-content">
                           <div class="calendar-installment-customer">
                              {{ getCustomerName(installment) }}
                           </div>
                           <div class="calendar-installment-amount">
                              {{ formatCurrency(installment.amount) }}
                           </div>
                        </div>
                        <div class="calendar-installment-status-indicator" :class="`status-${installment.status}`"></div>
                     </div>

                     <!-- Fazla taksit varsa göster -->
                     <div
                        v-if="day.installments.length > 3"
                        class="calendar-installment-more">
                        +{{ day.installments.length - 3 }}
                     </div>
                  </div>

                  <!-- Günlük toplam -->
                  <div
                     v-if="day.totalAmount > 0"
                     class="calendar-day-total">
                     <div class="calendar-total-amount">
                        {{ formatCurrency(day.totalAmount) }}
                     </div>
                     <div class="calendar-total-breakdown" v-if="day.breakdown">
                        <div class="total-breakdown-item paid" v-if="day.breakdown.paid > 0">
                          {{ formatCurrency(day.breakdown.paid) }}
                        </div>
                        <div class="total-breakdown-item pending" v-if="day.breakdown.pending > 0">
                          {{ formatCurrency(day.breakdown.pending) }}
                        </div>
                        <div class="total-breakdown-item overdue" v-if="day.breakdown.overdue > 0">
                          {{ formatCurrency(day.breakdown.overdue) }}
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </v-card-text>
   </Card>

   <!-- Gün detayları dialog -->
   <v-dialog
      v-model="dayDetailsDialog"
      max-width="600px">
      <v-card v-if="selectedDay" class="day-details-dialog">
         <v-card-title class="d-flex align-center">
            <span>{{ formatDate(selectedDay.date) }}</span>
            <v-spacer />
            <v-chip
               v-if="selectedDay.totalAmount > 0"
               size="small"
               color="primary">
               {{ formatCurrency(selectedDay.totalAmount) }}
            </v-chip>
         </v-card-title>

         <v-card-text>
            <div class="day-details-stats mb-4">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ selectedDay.installments.length }}</div>
                  <div class="stat-label">{{ t("app.totalInstallments") }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value paid">{{ selectedDay.breakdown?.paidCount || 0 }}</div>
                  <div class="stat-label">{{ t("app.paid") }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value pending">{{ selectedDay.breakdown?.pendingCount || 0 }}</div>
                  <div class="stat-label">{{ t("app.pending") }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value overdue">{{ selectedDay.breakdown?.overdueCount || 0 }}</div>
                  <div class="stat-label">{{ t("app.overdue") }}</div>
                </div>
              </div>
            </div>

            <div class="day-details-installments">
               <div
                  v-for="installment in selectedDay.installments"
                  :key="installment.id"
                  class="day-detail-installment"
                  :class="`installment-${installment.status}`"
                  @click="$router.push({ name: 'installmentDetail', params: { id: installment.id } })">
                  <div class="d-flex justify-space-between align-center">
                     <div class="installment-info">
                        <div class="installment-customer font-weight-medium">
                           {{ getCustomerName(installment) }}
                        </div>
                        <div class="installment-product text-caption text-medium-emphasis">
                           {{ installment.sale_product || "-" }}
                        </div>
                        <div class="installment-due-date text-caption">
                           {{ installment.due_at ? formatDate(new Date(installment.due_at)) : "-" }}
                        </div>
                     </div>
                     <div class="installment-details text-right">
                        <div class="installment-amount font-weight-bold">
                           {{ formatCurrency(installment.amount) }}
                        </div>
                        <v-chip
                           size="x-small"
                           :color="getStatusColor(installment.status)"
                           class="mt-1">
                           {{ getStatusText(installment.status) }}
                        </v-chip>
                     </div>
                  </div>
               </div>
            </div>
         </v-card-text>
         <v-card-actions>
            <v-spacer />
            <v-btn
               text
               @click="dayDetailsDialog = false">
               {{ t("app.close") }}
            </v-btn>
         </v-card-actions>
      </v-card>
   </v-dialog>
</template>

<script lang="ts" setup>
import Card from "@/components/Card/Card.vue";
import { IInstallment } from "@/services/InstallmentService";

interface CalendarDay {
   date: Date;
   isToday: boolean;
   installments: IInstallment[];
   totalAmount: number;
   hasOverdue: boolean;
   hasPaid: boolean;
   hasPending: boolean;
   breakdown?: {
      paid: number;
      pending: number;
      overdue: number;
      paidCount: number;
      pendingCount: number;
      overdueCount: number;
   };
}

interface Props {
   installments: IInstallment[];
   loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
   loading: false
});

const { t } = useI18n();

// Reactive state
const currentDate = ref(new Date());
const dayDetailsDialog = ref(false);
const selectedDay = ref<CalendarDay | null>(null);

// Computed properties
const isLoading = computed(() => props.loading);

// Mevcut haftanın başlangıç ve bitiş tarihleri
const currentWeekStart = computed(() => {
   const date = new Date(currentDate.value);
   const day = date.getDay();
   const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Pazartesi başlangıç
   return new Date(date.setDate(diff));
});

const currentWeekEnd = computed(() => {
   const date = new Date(currentWeekStart.value);
   date.setDate(date.getDate() + 6);
   return date;
});

// Bugünün haftasının başlangıcı
const todayWeekStart = computed(() => {
   const today = new Date();
   const day = today.getDay();
   const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Pazartesi başlangıç
   const start = new Date(today);
   start.setDate(start.getDate() - day + (day === 0 ? -6 : 1));
   return start;
});

// Mevcut hafta mı kontrolü
const isCurrentWeek = computed(() => {
   const currentStart = new Date(currentWeekStart.value);
   const todayStart = new Date(todayWeekStart.value);

   // Sadece yıl ve hafta numarasını karşılaştır
   return currentStart.getTime() === todayStart.getTime();
});

const currentWeekLabel = computed(() => {
   const start = currentWeekStart.value;
   const end = currentWeekEnd.value;

   if (start.getMonth() === end.getMonth()) {
      return `${start.getDate()}-${end.getDate()} ${start.toLocaleDateString('tr-TR', { month: 'long' })} ${start.getFullYear()}`;
   } else {
      return `${start.getDate()} ${start.toLocaleDateString('tr-TR', { month: 'short' })} - ${end.getDate()} ${end.toLocaleDateString('tr-TR', { month: 'short' })} ${start.getFullYear()}`;
   }
});

const weekDays = computed(() => [
   t("app.monday"),
   t("app.tuesday"),
   t("app.wednesday"),
   t("app.thursday"),
   t("app.friday"),
   t("app.saturday"),
   t("app.sunday")
]);

const weekDaysData = computed(() => {
   const today = new Date();
   const days: CalendarDay[] = [];
   const startDate = new Date(currentWeekStart.value);

   for (let i = 0; i < 7; i++) {
      const currentDateLoop = new Date(startDate);
      currentDateLoop.setDate(startDate.getDate() + i);

      const dateStr = currentDateLoop.toISOString().split('T')[0];
      const dayInstallments = props.installments.filter(installment => {
         if (!installment.due_at) return false;
         const dueDate = new Date(installment.due_at).toISOString().split('T')[0];
         return dueDate === dateStr;
      });

      const totalAmount = dayInstallments.reduce((sum, installment) => {
         return sum + parseFloat(installment.amount || '0');
      }, 0);

      // Durum bazlı toplamlar
      const breakdown = dayInstallments.reduce((acc, installment) => {
         const amount = parseFloat(installment.amount || '0');
         const status = installment.status as 'paid' | 'pending' | 'overdue';
         acc[status] += amount;
         acc[`${status}Count`] += 1;
         return acc;
      }, {
         paid: 0,
         pending: 0,
         overdue: 0,
         paidCount: 0,
         pendingCount: 0,
         overdueCount: 0
      });

      // Durum kontrolü
      const hasOverdue = dayInstallments.some(i => i.status === 'overdue');
      const hasPaid = dayInstallments.some(i => i.status === 'paid');
      const hasPending = dayInstallments.some(i => i.status === 'pending');

      days.push({
         date: new Date(currentDateLoop),
         isToday: currentDateLoop.toDateString() === today.toDateString(),
         installments: dayInstallments,
         totalAmount,
         hasOverdue,
         hasPaid,
         hasPending,
         breakdown
      });
   }

   return days;
});

// Methods
const previousWeek = () => {
   const newDate = new Date(currentDate.value);
   newDate.setDate(newDate.getDate() - 7);
   currentDate.value = newDate; // ✅ Doğru şekilde güncelleniyor
};

const nextWeek = () => {
   const newDate = new Date(currentDate.value);
   newDate.setDate(newDate.getDate() + 7);
   currentDate.value = newDate; // ✅ Doğru şekilde güncelleniyor
};

// Mevcut haftaya dön
const goToCurrentWeek = () => {
   currentDate.value = new Date(); // ✅ Doğru şekilde güncelleniyor
};

const showDayDetails = (day: CalendarDay) => {
   selectedDay.value = day;
   dayDetailsDialog.value = true;
};

const getCustomerName = (installment: IInstallment) => {
   if (installment.customer_name) {
      return `${installment.customer_name} ${installment.customer_surname}`;
   }
   return "-";
};

const formatCurrency = (value: string | number) => {
   const num = typeof value === "string" ? parseFloat(value) : value;
   return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY"
   }).format(num || 0);
};

const formatDate = (date: Date) => {
   return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      weekday: 'long'
   });
};

const getStatusColor = (status: string) => {
   switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'overdue': return 'error';
      default: return 'default';
   }
};

const getStatusText = (status: string) => {
   switch (status) {
      case 'paid': return t("app.paid");
      case 'pending': return t("app.pending");
      case 'overdue': return t("app.overdue");
      default: return status;
   }
};
</script>

<style scoped>
.installment-calendar-card {
   margin-bottom: 24px;
   border-radius: 12px;
   overflow: hidden;
   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.calendar-container {
   display: flex;
   flex-direction: column;
   height: 100%;
}

.calendar-header {
   display: grid;
   grid-template-columns: repeat(7, 1fr);
   border-bottom: 2px solid rgb(var(--v-theme-primary));
   background: linear-gradient(135deg, rgb(var(--v-theme-primary), 0.1) 0%, transparent 100%);
}

.calendar-day-header {
   padding: 16px 8px;
   text-align: center;
   font-weight: 600;
   font-size: 0.875rem;
   color: rgb(var(--v-theme-primary));
   text-transform: uppercase;
   letter-spacing: 0.5px;
}

.calendar-body {
   display: grid;
   grid-template-columns: repeat(7, 1fr);
   flex: 1;
   background-color: rgb(var(--v-theme-surface));
}

.weekly-view {
   grid-template-rows: 1fr;
}

.calendar-day {
   min-height: 160px;
   border: 1px solid rgb(var(--v-theme-surface-variant));
   padding: 12px;
   display: flex;
   flex-direction: column;
   position: relative;
   cursor: pointer;
   transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
   background: linear-gradient(135deg, transparent 0%, transparent 100%);
   overflow: hidden;
}

.calendar-day:hover {
   transform: translateY(-2px);
   box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
   border-color: rgb(var(--v-theme-primary));
   z-index: 1;
}

.calendar-day--today {
   background: linear-gradient(135deg, rgb(var(--v-theme-primary), 0.15) 0%, transparent 100%);
   border: 2px solid rgb(var(--v-theme-primary));
}

.calendar-day--has-installments {
   background: linear-gradient(135deg, rgb(var(--v-theme-success), 0.08) 0%, transparent 100%);
}

.calendar-day--overdue {
   background: linear-gradient(135deg, rgb(var(--v-theme-error), 0.1) 0%, transparent 100%);
   border-color: rgb(var(--v-theme-error), 0.3);
}

.calendar-day--paid {
   background: linear-gradient(135deg, rgb(var(--v-theme-success), 0.08) 0%, transparent 100%);
}

.calendar-day--pending {
   background: linear-gradient(135deg, rgb(var(--v-theme-warning), 0.08) 0%, transparent 100%);
}

.calendar-day-header-info {
   display: flex;
   justify-content: space-between;
   align-items: flex-start;
   margin-bottom: 12px;
}

.calendar-day-number {
   font-weight: 700;
   font-size: 1.3rem;
   color: rgb(var(--v-theme-on-surface));
   position: relative;
   display: flex;
   align-items: center;
   gap: 4px;
}

.calendar-day-month {
   font-size: 0.75rem;
   color: rgb(var(--v-theme-on-surface-variant));
   text-transform: uppercase;
   font-weight: 500;
}

.calendar-today-indicator {
   width: 8px;
   height: 8px;
   border-radius: 50%;
   background-color: rgb(var(--v-theme-primary));
   display: inline-block;
}

.calendar-installments {
   flex: 1;
   display: flex;
   flex-direction: column;
   gap: 6px;
   overflow: hidden;
}

.calendar-installment {
   background-color: rgb(var(--v-theme-surface));
   border-radius: 8px;
   padding: 8px 10px;
   font-size: 0.8rem;
   cursor: pointer;
   transition: all 0.2s ease;
   border-left: 4px solid;
   box-shadow: 0 2px 4px rgba(0,0,0,0.05);
   position: relative;
   overflow: hidden;
}

.calendar-installment:hover {
   transform: translateX(4px);
   box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.calendar-installment--paid {
   border-left-color: rgb(var(--v-theme-success));
   background: linear-gradient(90deg, rgb(var(--v-theme-success), 0.1) 0%, transparent 100%);
}

.calendar-installment--pending {
   border-left-color: rgb(var(--v-theme-warning));
   background: linear-gradient(90deg, rgb(var(--v-theme-warning), 0.1) 0%, transparent 100%);
}

.calendar-installment--overdue {
   border-left-color: rgb(var(--v-theme-error));
   background: linear-gradient(90deg, rgb(var(--v-theme-error), 0.1) 0%, transparent 100%);
}

.calendar-installment-content {
   display: flex;
   justify-content: space-between;
   align-items: center;
}

.calendar-installment-customer {
   font-weight: 500;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   max-width: 70%;
}

.calendar-installment-amount {
   font-weight: 700;
   color: rgb(var(--v-theme-primary));
   font-size: 0.85rem;
}

.calendar-installment-status-indicator {
   position: absolute;
   top: 0;
   right: 0;
   width: 8px;
   height: 100%;
}

.status-paid {
   background: linear-gradient(to bottom, rgb(var(--v-theme-success)), rgb(var(--v-theme-success), 0.5));
}

.status-pending {
   background: linear-gradient(to bottom, rgb(var(--v-theme-warning)), rgb(var(--v-theme-warning), 0.5));
}

.status-overdue {
   background: linear-gradient(to bottom, rgb(var(--v-theme-error)), rgb(var(--v-theme-error), 0.5));
}

.calendar-installment-more {
   background-color: rgb(var(--v-theme-primary), 0.1);
   color: rgb(var(--v-theme-primary));
   border-radius: 6px;
   padding: 6px 10px;
   font-size: 0.75rem;
   text-align: center;
   cursor: pointer;
   font-weight: 600;
   margin-top: 6px;
   transition: all 0.2s ease;
}

.calendar-installment-more:hover {
   background-color: rgb(var(--v-theme-primary), 0.2);
   transform: scale(1.05);
}

.calendar-day-total {
   margin-top: auto;
   padding: 10px 8px;
   border-radius: 8px;
   font-weight: 700;
   font-size: 0.9rem;
   text-align: center;
   background: linear-gradient(135deg, rgb(var(--v-theme-primary), 0.2) 0%, rgb(var(--v-theme-primary), 0.1) 100%);
   border: 1px solid rgb(var(--v-theme-primary), 0.2);
   backdrop-filter: blur(10px);
}

.calendar-total-amount {
   color: rgb(var(--v-theme-primary));
   font-size: 1.1rem;
   margin-bottom: 6px;
}

.calendar-total-breakdown {
   display: flex;
   gap: 6px;
   justify-content: center;
   flex-wrap: wrap;
}

.total-breakdown-item {
   font-size: 0.7rem;
   padding: 3px 8px;
   border-radius: 6px;
   font-weight: 600;
   min-width: 60px;
}

.total-breakdown-item.paid {
   background-color: rgb(var(--v-theme-success), 0.2);
   color: rgb(var(--v-theme-success));
}

.total-breakdown-item.pending {
   background-color: rgb(var(--v-theme-warning), 0.2);
   color: rgb(var(--v-theme-warning));
}

.total-breakdown-item.overdue {
   background-color: rgb(var(--v-theme-error), 0.2);
   color: rgb(var(--v-theme-error));
}

/* Dialog Styles */
.day-details-dialog {
   border-radius: 16px;
   overflow: hidden;
   box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.day-details-stats {
   background: linear-gradient(135deg, rgb(var(--v-theme-primary), 0.1) 0%, transparent 100%);
   border-radius: 12px;
   padding: 16px;
}

.stats-grid {
   display: grid;
   grid-template-columns: repeat(4, 1fr);
   gap: 16px;
}

.stat-item {
   text-align: center;
}

.stat-value {
   font-size: 1.5rem;
   font-weight: 700;
   color: rgb(var(--v-theme-primary));
}

.stat-value.paid {
   color: rgb(var(--v-theme-success));
}

.stat-value.pending {
   color: rgb(var(--v-theme-warning));
}

.stat-value.overdue {
   color: rgb(var(--v-theme-error));
}

.stat-label {
   font-size: 0.75rem;
   color: rgb(var(--v-theme-on-surface-variant));
   margin-top: 4px;
}

.day-details-installments {
   max-height: 400px;
   overflow-y: auto;
   padding: 8px 0;
}

.day-detail-installment {
   padding: 16px;
   border: 1px solid rgb(var(--v-theme-surface-variant));
   border-radius: 12px;
   margin-bottom: 12px;
   cursor: pointer;
   transition: all 0.3s ease;
   background: linear-gradient(135deg, transparent 0%, transparent 100%);
}

.day-detail-installment:hover {
   background: linear-gradient(135deg, rgb(var(--v-theme-primary), 0.05) 0%, transparent 100%);
   transform: translateY(-2px);
   box-shadow: 0 6px 12px rgba(0,0,0,0.08);
   border-color: rgb(var(--v-theme-primary));
}

.installment-info {
   flex: 1;
}

.installment-customer {
   color: rgb(var(--v-theme-on-surface));
}

.installment-product {
   color: rgb(var(--v-theme-on-surface-variant));
}

.installment-due-date {
   color: rgb(var(--v-theme-primary));
   font-weight: 500;
}

.installment-details {
   min-width: 100px;
}

.installment-amount {
   color: rgb(var(--v-theme-primary));
   font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 1200px) {
   .calendar-day {
      min-height: 140px;
      padding: 10px;
   }

   .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
   }
}

@media (max-width: 768px) {
   .calendar-day {
      min-height: 120px;
      padding: 8px;
   }

   .calendar-day-number {
      font-size: 1.1rem;
   }

   .calendar-installment {
      padding: 6px 8px;
      font-size: 0.75rem;
   }

   .calendar-day-total {
      font-size: 0.8rem;
      padding: 8px 6px;
   }

   .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
   }

   .stat-value {
      font-size: 1.2rem;
   }

   .calendar-day-header-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
   }

   .calendar-day-month {
      font-size: 0.7rem;
   }
}

@media (max-width: 480px) {
   .calendar-day {
      min-height: 100px;
      padding: 6px;
   }

   .calendar-installments {
      gap: 4px;
   }

   .calendar-installment-more {
      font-size: 0.7rem;
      padding: 4px 6px;
   }

   .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 6px;
   }

   .stat-item {
      padding: 6px 4px;
   }

   .calendar-total-breakdown {
      gap: 4px;
   }

   .total-breakdown-item {
      font-size: 0.65rem;
      padding: 2px 6px;
      min-width: 50px;
   }
}
</style>