<template>
   <v-card elevation="2" class="recent-sales-widget">
      <v-card-title class="d-flex align-center justify-space-between">
         <div>
            <h3 class="text-h6 font-weight-bold">{{ t("app.recentSales") }}</h3>
            <p class="text-caption text-medium-emphasis ma-0">{{ t("app.last5Sales") }}</p>
         </div>
         <v-btn
            v-bind:to="{ name: 'saleList' }"
            variant="text"
            size="small"
            color="primary">
            {{ t("app.viewAll") }}
            <v-icon end icon="$chevronRight" size="16" />
         </v-btn>
      </v-card-title>

      <v-card-text class="pa-0">
         <div v-if="loading" class="d-flex justify-center align-center pa-6" style="height: 200px;">
            <v-progress-circular indeterminate color="primary" />
         </div>

         <div v-else-if="sales.length === 0" class="d-flex justify-center align-center pa-6" style="height: 200px;">
            <div class="text-center">
               <v-icon icon="$shoppingBag" size="48" color="grey-lighten-1" class="mb-2" />
               <p class="text-body-2 text-medium-emphasis">{{ t("app.noRecentSales") }}</p>
            </div>
         </div>

         <v-list v-else class="pa-0">
            <template v-for="(sale, index) in sales" :key="sale.id">
               <v-list-item
                  v-bind:to="{ name: 'saleDetail', params: { id: sale.id } }"
                  class="px-4 py-3"
                  :class="{ 'border-b': index < sales.length - 1 }">
                  <template #prepend>
                     <v-avatar
                        size="40"
                        :color="getStatusColor(sale)"
                        variant="tonal">
                        <v-icon icon="$receipt" size="20" />
                     </v-avatar>
                  </template>

                  <v-list-item-title class="font-weight-medium">
                     {{ sale.customer?.name || t("app.unknownCustomer") }}
                  </v-list-item-title>

                  <v-list-item-subtitle class="d-flex align-center mt-1">
                     <span class="me-2">{{ formatDate(sale.created_at) }}</span>
                     <v-chip
                        :color="getStatusColor(sale)"
                        size="x-small"
                        variant="flat">
                        {{ getStatusText(sale) }}
                     </v-chip>
                  </v-list-item-subtitle>

                  <template #append>
                     <div class="text-right">
                        <div class="text-h6 font-weight-bold">
                           {{ formatCurrency(sale.price) }}
                        </div>
                        <div class="text-caption text-medium-emphasis">
                           {{ sale.installment ? `${sale.installment} ${t("app.installment")}` : t("app.cash") }}
                        </div>
                     </div>
                  </template>
               </v-list-item>
            </template>
         </v-list>
      </v-card-text>
   </v-card>
</template>

<script lang="ts" setup>
import type { ISale } from "@/services/SaleService";

interface Props {
   sales: ISale[];
   loading?: boolean;
}

withDefaults(defineProps<Props>(), {
   loading: false
});

const { t } = useI18n();

// Helper functions
function formatCurrency(value: string | number) {
   const num = typeof value === "string" ? parseFloat(value) : value;
   return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY"
   }).format(num || 0);
}

function formatDate(dateString?: string) {
   if (!dateString) return "";
   return new Date(dateString).toLocaleDateString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
   });
}

function getStatusColor(sale: ISale) {
   if (sale.installment && sale.installment > 0) {
      return 'warning'; // Installment sale
   }
   return 'success'; // Cash sale
}

function getStatusText(sale: ISale) {
   if (sale.installment && sale.installment > 0) {
      return t('app.installment');
   }
   return t('app.cash');
}
</script>

<style scoped>
.recent-sales-widget {
   height: 100%;
}

.border-b {
   border-bottom: 1px solid rgba(var(--v-theme-outline), 0.12);
}

.v-list-item {
   transition: background-color 0.2s ease;
}

.v-list-item:hover {
   background-color: rgba(var(--v-theme-primary), 0.04);
}
</style>