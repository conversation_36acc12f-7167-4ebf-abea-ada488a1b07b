<template>
   <v-menu offset="3, 0">
      <template v-slot:activator="{ props }">
         <v-btn
            v-bind="props"
            v-bind:loading="loading"
            icon="$translate" />
      </template>

      <v-list
         v-bind:slim="false"
         class="select-none">
         <v-list-item
            v-for="(locale, key) in appConfig.language.locales"
            @click="translateHandler(key)">
            <template v-slot:append>
               <v-icon v-html="locale.flag" />
            </template>

            <template v-slot:title>
               {{ locale.name }}
            </template>
         </v-list-item>
      </v-list>
   </v-menu>
</template>

<script lang="ts" setup>
const loading = ref(false);

const translateHandler = async (locale: string) => {
   loading.value = true;
   await loadLocale(locale)
      .then(() => {
         setLocale(locale);
      })
      .finally(() => {
         loading.value = false;
      });
};
</script>
