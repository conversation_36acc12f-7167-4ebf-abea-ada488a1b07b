<?php

declare(strict_types=1);

namespace App\Modules\Asset\Taxrate;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Asset\Taxrate\TaxrateService;
use App\Modules\Asset\Taxrate\TaxrateRequest;
use App\Modules\Asset\Taxrate\TaxrateResponse;

/**
 * @OA\Tag(name="Tax", description="Vergi işlemleri")
 */
class TaxrateController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected TaxrateService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Tax"}, path="/tax/", summary="Vergi listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllTax() {
      $this->response(function () {
         $result = $this->service->getAll();

         return array_map(function ($item) {
            $response = new TaxrateResponse();
            $response->fromArray($item);

            return $response;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Tax"}, path="/tax/{id}", summary="Vergi detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getTax(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id);

         $response = new TaxrateResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Post(tags={"Tax"}, path="/tax/", summary="Vergi ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"title", "rate"},
    *       @OA\Property(property="title", type="string", example="KDV %18"),
    *       @OA\Property(property="rate", type="number", example=18.00)
    *    ))
    * )
    */
   public function createTax() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new TaxrateRequest();
         $request->fromArray($json);
         $result = $this->service->createTax($request);

         $response = new TaxrateResponse();
         $response->fromArray($result);

         return $response;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Tax"}, path="/tax/", summary="Vergi güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "title", "rate"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="title", type="string", example="KDV %18"),
    *       @OA\Property(property="rate", type="number", example=18.00)
    *    ))
    * )
    */
   public function updateTax() {
      $this->response(function () {
         $json = $this->request->json();

         $request = new TaxrateRequest();
         $request->fromArray($json);
         $result = $this->service->updateTax($request);

         $response = new TaxrateResponse();
         $response->fromArray($result);

         return $response;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Tax"}, path="/tax/{id}", summary="Vergi sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteTax(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete([
            'id' => $id,
            'deleted_at' => ['IS NULL']
         ]);

         return $result;
      });
   }
}