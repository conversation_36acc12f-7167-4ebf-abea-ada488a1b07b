export const productMenu: TList[] = [
   {
      itemType: "subheader",
      itemTitle: "module.asset"
   },
   {
      itemType: "divider"
   },
   {
      itemTitle: i18n.global.t("module.product"),
      itemProps: {
         to: "/asset/product",
         prependIcon: "$product"
      }
   },
   {
      itemTitle: i18n.global.t("module.competing"),
      itemProps: {
         to: "/asset/competing",
         prependIcon: "$competing"
      }
   },
   {
      itemTitle: i18n.global.t("module.company"),
      itemProps: {
         prependIcon: "$company"
      }
   },
   {
      itemTitle: i18n.global.t("module.employee"),
      itemProps: {
         prependIcon: "$employee"
      }
   },
   {
      itemTitle: i18n.global.t("module.user"),
      itemProps: {
         prependIcon: "$user"
      }
   },
   {
      itemTitle: i18n.global.t("module.equipment"),
      itemProps: {
         prependIcon: "$equipment"
      }
   },
   {
      itemTitle: i18n.global.t("module.component"),
      itemProps: {
         to: "/asset/component",
         prependIcon: "$component"
      }
   },
   {
      itemTitle: i18n.global.t("module.license"),
      itemProps: {
         prependIcon: "$license"
      }
   },
   {
      itemTitle: i18n.global.t("module.subscription"),
      itemProps: {
         prependIcon: "$subscription"
      }
   },
   {
      itemTitle: i18n.global.t("module.definition"),
      itemProps: {
         prependIcon: "$definitions",
         value: "asset-definitions"
      },
      children: [
         {
            itemTitle: i18n.global.t("module.category"),
            itemProps: {
               to: "/asset/category"
            }
         },
         {
            itemTitle: i18n.global.t("module.manufacturer"),
            itemProps: {
               to: "/asset/manufacturer"
            }
         },
         {
            itemTitle: i18n.global.t("module.standard"),
            itemProps: {
               to: "/asset/standard"
            }
         },
         {
            itemTitle: i18n.global.t("module.attribute"),
            itemProps: {
               to: "/asset/attribute"
            }
         },
         {
            itemTitle: i18n.global.t("module.taxrate"),
            itemProps: {
               to: "/asset/taxrate"
            }
         },
         {
            itemTitle: i18n.global.t("module.unit"),
            itemProps: {
               to: "/asset/unit"
            }
         },
         {
            itemTitle: i18n.global.t("module.department"),
            itemProps: {
               to: "/asset/department"
            }
         },
         {
            itemTitle: i18n.global.t("module.location"),
            itemProps: {
               to: "/asset/location"
            }
         },
         {
            itemTitle: i18n.global.t("module.group"),
            itemProps: {
               to: "/asset/group"
            }
         }
      ]
   }
];
