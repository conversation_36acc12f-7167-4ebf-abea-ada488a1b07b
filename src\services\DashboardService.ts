export interface ISaleTotal {
   total_sales: number;
   current_debit: number;
}

export interface ISaleTotalParams {
   start_date: MaybeRef<Date>;
   end_date: MaybeRef<Date>;
}

export interface IExpectedPayments {
   total_pending_amount: number;
   total_overdue_amount: number;
}

export interface IExpectedPaymentsParams {
   start_date: MaybeRef<Date>;
   end_date: MaybeRef<Date>;
}

export const useGetDashboardTotalSale = (payload?: TQuery<ISaleTotalParams>) => {
   const options = computed(() => ({
      queryKey: ["sale", "totalSale", payload?.params?.start_date],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get("/sale/totalAmount", { signal, params: deepValue(payload?.params) })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetDashboardActiveCustomer = (payload?: TQuery<any>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerActive"],
      queryFn: async () => {
         return (await appAxios.get("/customer/active")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetDashboardExpectedPayments = (payload?: TQuery<IExpectedPaymentsParams>) => {
   const options = computed(() => ({
      queryKey: ["sale", "expectedPayments", payload?.params?.start_date],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get("/sale/expectedPayments", { signal, params: deepValue(payload?.params) })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};
