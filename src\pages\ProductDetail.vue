<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-spacer />
            <RecordButton
               v-if="product.id"
               v-bind:disabled="isLoading || isPending"
               color="error"
               prepend-icon="$trash"
               @click="removeHandler">
               {{ t("app.delete") }}
            </RecordButton>
            <RecordButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ product.id ? t("app.update") : t("app.save") }}
            </RecordButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.title"
                     v-bind:rules="[appRules.required()]"></v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput v-model="product.price" />
               </v-col>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.stock") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model.number="product.stock" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="product.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ product.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="product.category"
                     v-bind:items="categoryAll"
                     v-bind:loading="categoryLoading"
                     item-value="id"
                     return-object />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import RecordButton from "@/components/Button/RecordButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import NumberInput from "@/components/Input/NumberInput.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import { appRules } from "@/utils/rules";
import { useGetCategoryAll } from "../services/CategoryService";
import { IProduct, IProductStore, useCreateProduct, useDeleteProduct, useGetProductById, useUpdateProduct } from "../services/ProductService";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const product = ref<IProduct>({
   is_active: 1,
   stock: 0
} as IProduct);
const routeId = computed(() => route.params.id);
const enabled = computed(() => !!routeId.value);

// services
const getProductById = useGetProductById({
   id: routeId,
   enabled: enabled,
   onSuccess: (item) => {
      product.value = { ...item };
   }
});
const updateProduct = useUpdateProduct();
const createProduct = useCreateProduct();
const deleteProduct = useDeleteProduct();

// relation services
const { data: categoryAll, isLoading: categoryLoading } = useGetCategoryAll();

// status
const isLoading = computed(() => getProductById.isLoading.value);
const isPending = computed(() => createProduct.isPending.value || updateProduct.isPending.value);
const isError = computed(() => getProductById.isError.value);
const isSuccess = computed(() => createProduct.isSuccess.value);

// handlers
const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteRecord")
      });

      if (confirm) {
         await deleteProduct.mutateAsync(product.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "productList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: IProductStore = {
      code: product.value.code,
      title: product.value.title,
      is_active: product.value.is_active,
      category_id: product.value.category?.id || null,
      price: product.value.price,
      installment_price: 0,
      stock: product.value.stock
   };

   try {
      if (!product.value.id) {
         await createProduct.mutateAsync(payload, {
            onSuccess: () => {
               snackbarStore.add({ text: t("app.recordCreated") });
               router.push({ name: "productList" });
            }
         });
      } else {
         await updateProduct.mutateAsync({ id: product.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
