<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading && isFirst">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isEdit ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("app.basicInfo") }}
         </template>

         <template v-slot:items>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="standard.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="standard.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="standard.content"
                     v-bind:rules="[appRules.required()]"
                     class="max-grow-80"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="standard.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="[standard.image_path]" />
                  <ImageUpload v-model="imageUpload" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useCreateStandard, useGetStandardById, useUpdateStandard } from "../services/StandardService";
import { IStandard, IStandardStore } from "../utils/types";

// hooks
const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// states
const standard = ref({
   sort_order: 0
} as IStandard);
const routeId = computed(() => route.params.id);
const isEdit = computed(() => !!routeId.value);
const language = ref(1);
const imageUpload = ref([] as File[]);

// services
const getStandardById = useGetStandardById({
   enabled: isEdit,
   params: {
      id: routeId,
      language: language
   },
   onSuccess: (data) => {
      standard.value = { ...data };
   }
});
const updateStandard = useUpdateStandard();
const createStandard = useCreateStandard();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["standard", "standardById"] });

// status
const isLoading = computed(() => getStandardById.isLoading.value);
const isFirst = computed(() => getStandardById.isFirst.value);
const isPending = computed(() => createStandard.isPending.value || updateStandard.isPending.value);
const isError = computed(() => getStandardById.isError.value);

// mutates
const deleteImageMutate = async () => {
   return await deleteImage.mutateAsync({
      id: standard.value.id,
      table: "asset_standard"
   });
};

const uploadImageMutate = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "standard"
   });
};

// handlers
const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirm"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteImageMutate();
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: IStandardStore = {
      sort_order: standard.value.sort_order,
      translate: [
         {
            language_id: language.value,
            title: standard.value.title,
            content: standard.value.content
         }
      ]
   };

   try {
      if (imageUpload.value.length) {
         if (standard.value.image_path) {
            await deleteImageMutate();
            snackbarStore.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadImageMutate();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isEdit.value) {
         await updateStandard.mutateAsync({ id: standard.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      } else {
         await createStandard.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "asset.standardDetail", params: { id: data.data.id } });
               snackbarStore.add({ text: t("app.recordCreated") });
            }
         });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
