<template>
   <Container v-bind:loading="isLoading">
      <Card>
         <template v-slot:header>
            <SearchInput v-model:search="filter" />
         </template>

         <template v-slot:actions>
            <ActionButton
               v-bind:disabled="isLoading"
               v-bind:to="{ name: 'asset.productCreate' }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:title>
            {{ t("module.productList") }}
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            @row:click="(item) => $router.push({ name: 'asset.productDetail', params: { id: item.id } })">
            <template v-slot:item.is_active="{ item }">
               <v-chip v-bind:color="item.is_active ? 'success' : undefined">
                  {{ item.is_active ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>

            <template v-slot:item.category_list="{ item }">
               {{ item.category_list.map((category: any) => category.title).join(", ") }}
            </template>

            <template v-slot:item.manufacturer_list="{ item }">
               {{ item.manufacturer_list.map((manufacturer: any) => manufacturer.title).join(", ") }}
            </template>

            <template v-slot:item.actions="{ item }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="console.log(item)" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetProductAll } from "../services/ProductService";
import { IProduct } from "../utils/types";

// hooks
const { t } = useI18n();

// states
const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IProduct>[] => [
   { title: t("app.code"), key: "code", width: "100" },
   { title: t("app.title"), key: "title" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("module.category"), key: "category_list", width: "350" },
   { title: t("module.manufacturer"), key: "manufacturer_list", width: "150" }
]);

// services
const { data, isLoading } = useGetProductAll();
</script>
